<?php
/**
 * Setup Payment Features
 * Install payment method features for overdue book fines
 */

require_once 'config/database.php';
require_once 'config/config.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    $database = new Database();
    $db = $database->getConnection();

    echo "<h1>🔧 Setting Up Payment Features</h1>";
    echo "<p>Installing enhanced payment method features for overdue book fines...</p>";

    // Check if fines table exists
    $check_fines = $db->query("SHOW TABLES LIKE 'fines'");
    if ($check_fines->rowCount() == 0) {
        echo "<h3>Creating fines table...</h3>";

        $create_fines = "CREATE TABLE `fines` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `member_id` int(11) NOT NULL,
            `loan_id` int(11) DEFAULT NULL,
            `amount` decimal(10,2) NOT NULL,
            `status` enum('unpaid','paid','waived') DEFAULT 'unpaid',
            `payment_method` enum('cash','gcash','paymaya','bank_transfer','credit_card','debit_card') DEFAULT NULL,
            `payment_reference` varchar(100) DEFAULT NULL,
            `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `paid_date` timestamp NULL DEFAULT NULL,
            `waived_date` timestamp NULL DEFAULT NULL,
            `processed_by` int(11) DEFAULT NULL,
            `notes` text DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `idx_member_id` (`member_id`),
            KEY `idx_loan_id` (`loan_id`),
            KEY `idx_status` (`status`),
            KEY `idx_created_date` (`created_date`),
            KEY `idx_payment_method` (`payment_method`),
            KEY `idx_processed_by` (`processed_by`),
            FOREIGN KEY (`member_id`) REFERENCES `members`(`id`) ON DELETE CASCADE,
            FOREIGN KEY (`processed_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        $db->exec($create_fines);
        echo "<p>✅ Fines table created successfully.</p>";
    } else {
        echo "<h3>Updating fines table...</h3>";

        // Add new columns if they don't exist
        $columns_to_add = [
            'payment_method' => "ALTER TABLE `fines` ADD COLUMN `payment_method` enum('cash','gcash','paymaya','bank_transfer','credit_card','debit_card') DEFAULT NULL",
            'payment_reference' => "ALTER TABLE `fines` ADD COLUMN `payment_reference` varchar(100) DEFAULT NULL",
            'processed_by' => "ALTER TABLE `fines` ADD COLUMN `processed_by` int(11) DEFAULT NULL"
        ];

        foreach ($columns_to_add as $column => $sql) {
            try {
                $check_column = $db->query("SHOW COLUMNS FROM `fines` LIKE '$column'");
                if ($check_column->rowCount() == 0) {
                    $db->exec($sql);
                    echo "<p>✅ Added column '$column' to fines table.</p>";
                } else {
                    echo "<p>ℹ️ Column '$column' already exists in fines table.</p>";
                }
            } catch (Exception $e) {
                echo "<p>⚠️ Could not add column '$column': " . $e->getMessage() . "</p>";
            }
        }

        // Add indexes if they don't exist
        $indexes_to_add = [
            'idx_payment_method' => "CREATE INDEX `idx_payment_method` ON `fines` (`payment_method`)",
            'idx_processed_by' => "CREATE INDEX `idx_processed_by` ON `fines` (`processed_by`)"
        ];

        foreach ($indexes_to_add as $index => $sql) {
            try {
                $db->exec($sql);
                echo "<p>✅ Added index '$index' to fines table.</p>";
            } catch (Exception $e) {
                // Index might already exist
                echo "<p>ℹ️ Index '$index' already exists or could not be created.</p>";
            }
        }
    }

    // Check if payment_transactions table exists
    $check_transactions = $db->query("SHOW TABLES LIKE 'payment_transactions'");
    if ($check_transactions->rowCount() == 0) {
        echo "<h3>Creating payment_transactions table...</h3>";

        $create_transactions = "CREATE TABLE `payment_transactions` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `fine_id` int(11) NOT NULL,
            `member_id` int(11) NOT NULL,
            `amount` decimal(10,2) NOT NULL,
            `payment_method` enum('cash','gcash','paymaya','bank_transfer','credit_card','debit_card') NOT NULL,
            `payment_reference` varchar(100) DEFAULT NULL,
            `transaction_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `processed_by` int(11) NOT NULL,
            `receipt_number` varchar(50) UNIQUE NOT NULL,
            `status` enum('pending','completed','failed','refunded') DEFAULT 'completed',
            `notes` text DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `idx_fine_id` (`fine_id`),
            KEY `idx_member_id` (`member_id`),
            KEY `idx_payment_method` (`payment_method`),
            KEY `idx_transaction_date` (`transaction_date`),
            KEY `idx_receipt_number` (`receipt_number`),
            KEY `idx_processed_by` (`processed_by`),
            FOREIGN KEY (`fine_id`) REFERENCES `fines`(`id`) ON DELETE CASCADE,
            FOREIGN KEY (`member_id`) REFERENCES `members`(`id`) ON DELETE CASCADE,
            FOREIGN KEY (`processed_by`) REFERENCES `users`(`id`) ON DELETE RESTRICT
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        $db->exec($create_transactions);
        echo "<p>✅ Payment transactions table created successfully.</p>";
    } else {
        echo "<p>ℹ️ Payment transactions table already exists.</p>";
    }

    // Check if settings table exists and add payment settings
    $check_settings = $db->query("SHOW TABLES LIKE 'settings'");
    if ($check_settings->rowCount() > 0) {
        echo "<h3>Adding payment settings...</h3>";

        // Check if description column exists
        $check_desc = $db->query("SHOW COLUMNS FROM `settings` LIKE 'description'");
        $has_description = $check_desc->rowCount() > 0;

        $payment_settings = [
            ['financial', 'enable_payment_methods', 'true'],
            ['financial', 'default_payment_method', 'cash'],
            ['financial', 'require_payment_reference', 'false'],
            ['financial', 'auto_generate_receipts', 'true']
        ];

        if ($has_description) {
            // Table has description column
            $insert_setting = $db->prepare("INSERT INTO `settings` (`setting_group`, `setting_key`, `setting_value`, `description`) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE `setting_value` = VALUES(`setting_value`)");
            $descriptions = [
                'Enable multiple payment methods for fines',
                'Default payment method for fine payments',
                'Require payment reference for non-cash payments',
                'Automatically generate payment receipts'
            ];

            foreach ($payment_settings as $index => $setting) {
                $setting[] = $descriptions[$index];
                $insert_setting->execute($setting);
                echo "<p>✅ Added/updated setting: {$setting[1]}</p>";
            }
        } else {
            // Table doesn't have description column
            $insert_setting = $db->prepare("INSERT INTO `settings` (`setting_group`, `setting_key`, `setting_value`) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE `setting_value` = VALUES(`setting_value`)");

            foreach ($payment_settings as $setting) {
                $insert_setting->execute($setting);
                echo "<p>✅ Added/updated setting: {$setting[1]}</p>";
            }
        }
    } else {
        echo "<p>⚠️ Settings table not found. Payment settings not added.</p>";
    }

    // Create sample fines from existing book_loans with fines
    echo "<h3>Migrating existing fines...</h3>";

    $existing_fines = $db->query("SELECT * FROM book_loans WHERE fine > 0 AND status = 'returned'");
    $migrated = 0;

    $insert_fine = $db->prepare("INSERT INTO fines (member_id, loan_id, amount, status, created_date) VALUES (?, ?, ?, 'paid', ?) ON DUPLICATE KEY UPDATE amount = VALUES(amount)");

    foreach ($existing_fines as $loan) {
        try {
            $insert_fine->execute([
                $loan['member_id'],
                $loan['id'],
                $loan['fine'],
                $loan['return_date'] ?: $loan['updated_at']
            ]);
            $migrated++;
        } catch (Exception $e) {
            // Fine might already exist
        }
    }

    echo "<p>✅ Migrated $migrated existing fines to new system.</p>";

    // Create sample unpaid fines from overdue loans
    echo "<h3>Creating fines for overdue books...</h3>";

    $overdue_loans = $db->query("SELECT bl.*, DATEDIFF(CURDATE(), bl.due_date) as days_overdue
                                FROM book_loans bl
                                WHERE bl.status = 'borrowed'
                                AND bl.due_date < CURDATE()
                                AND bl.id NOT IN (SELECT loan_id FROM fines WHERE loan_id IS NOT NULL)");

    $created = 0;
    $fine_per_day = 1.00; // $1 per day

    foreach ($overdue_loans as $loan) {
        if ($loan['days_overdue'] > 0) {
            $fine_amount = $loan['days_overdue'] * $fine_per_day;

            try {
                $insert_fine->execute([
                    $loan['member_id'],
                    $loan['id'],
                    $fine_amount,
                    'unpaid'
                ]);
                $created++;
            } catch (Exception $e) {
                // Fine might already exist
            }
        }
    }

    echo "<p>✅ Created $created new fines for overdue books.</p>";

    echo "<h2>🎉 Payment Features Setup Complete!</h2>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 20px 0;'>";
    echo "<h3>✅ What's Now Available:</h3>";
    echo "<ul>";
    echo "<li><strong>Payment Processing:</strong> Process fine payments with multiple payment methods</li>";
    echo "<li><strong>Payment Reports:</strong> View detailed payment reports and member payment history</li>";
    echo "<li><strong>Receipt Generation:</strong> Automatic receipt generation for all payments</li>";
    echo "<li><strong>Member Interface:</strong> Members can view their fines and payment history</li>";
    echo "<li><strong>Payment Methods:</strong> Cash, GCash, PayMaya, Bank Transfer, Credit Card, Debit Card</li>";
    echo "</ul>";
    echo "</div>";

    echo "<div style='background: #cce7ff; border: 1px solid #99d6ff; border-radius: 5px; padding: 15px; margin: 20px 0;'>";
    echo "<h3>🔗 Access Links:</h3>";
    echo "<ul>";
    echo "<li><a href='admin/payment_processing.php'><strong>Admin: Payment Processing</strong></a> - Process fine payments</li>";
    echo "<li><a href='admin/payment_reports.php'><strong>Admin: Payment Reports</strong></a> - View payment reports</li>";
    echo "<li><a href='admin/financial_management.php'><strong>Admin: Financial Overview</strong></a> - Financial management</li>";
    echo "<li><a href='members/my_fines.php'><strong>Members: My Fines & Payments</strong></a> - Member fine history</li>";
    echo "</ul>";
    echo "</div>";

    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;'>";
    echo "<h3>👥 Who Can Access Payment Records:</h3>";
    echo "<ul>";
    echo "<li><strong>Admin Users:</strong> Full access to all payment processing, reports, and financial management</li>";
    echo "<li><strong>Librarian Users:</strong> Full access to payment processing and reports (same as admin)</li>";
    echo "<li><strong>Members:</strong> Can view their own fines and payment history only</li>";
    echo "</ul>";
    echo "<p><strong>Note:</strong> Both Admin and Librarian users can now process payments, view reports, and manage financial data.</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<h2>❌ Error Setting Up Payment Features</h2>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database connection and try again.</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}
h1, h2, h3 {
    color: #333;
}
p {
    margin: 10px 0;
}
ul {
    margin: 10px 0;
    padding-left: 30px;
}
a {
    color: #007bff;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>

<?php
require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html>
<head>
    <title>Create Test Data</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css'>
</head>
<body class='bg-light'>
<div class='container mt-4'>
    <div class='card'>
        <div class='card-header bg-primary text-white'>
            <h3><i class='bi bi-database-add me-2'></i>Create Test Data for Enhanced Member Dashboard</h3>
        </div>
        <div class='card-body'>";

try {
    // Create sample books if they don't exist
    $query = "SELECT COUNT(*) as book_count FROM books";
    $stmt = $db->query($query);
    $book_count = $stmt->fetch()['book_count'];
    
    if ($book_count < 5) {
        echo "<h5>Creating Sample Books...</h5>";
        
        $books = [
            ['To Kill a Mockingbird', 'Harper Lee', '9780061120084', 'Fiction', 'A classic novel about racial injustice in the American South.'],
            ['1984', 'George Orwell', '9780451524935', 'Fiction', 'A dystopian social science fiction novel.'],
            ['Pride and Prejudice', 'Jane Austen', '9780141439518', 'Romance', 'A romantic novel of manners.'],
            ['The Great Gatsby', 'F. Scott Fitzgerald', '9780743273565', 'Fiction', 'A classic American novel set in the Jazz Age.'],
            ['Harry Potter and the Sorcerer\'s Stone', 'J.K. Rowling', '9780439708180', 'Fantasy', 'The first book in the Harry Potter series.']
        ];
        
        foreach ($books as $book) {
            $query = "INSERT IGNORE INTO books (title, author, isbn, category, description, total_quantity, available_quantity, publication_year) 
                      VALUES (?, ?, ?, ?, ?, 3, 3, 2020)";
            $stmt = $db->prepare($query);
            $stmt->execute($book);
            echo "<p class='text-success'>✅ Added book: {$book[0]} by {$book[1]}</p>";
        }
    } else {
        echo "<p class='text-info'>✅ Books already exist ($book_count books found)</p>";
    }
    
    // Create test member with password
    $query = "SELECT * FROM members WHERE email = '<EMAIL>'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        echo "<h5>Creating Test Member...</h5>";
        $query = "INSERT INTO members (first_name, last_name, email, password, phone, address, membership_date, membership_status) 
                  VALUES ('Test', 'Member', '<EMAIL>', ?, '************', '123 Test Street', NOW(), 'active')";
        $stmt = $db->prepare($query);
        $password = password_hash('member123', PASSWORD_DEFAULT);
        $stmt->execute([$password]);
        echo "<p class='text-success'>✅ Test member created: <EMAIL> / member123</p>";
    } else {
        echo "<p class='text-info'>✅ Test member already exists</p>";
    }
    
    // Get test member ID
    $query = "SELECT id FROM members WHERE email = '<EMAIL>'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $member = $stmt->fetch();
    $member_id = $member['id'];
    
    // Create sample loans for testing
    $query = "SELECT COUNT(*) as loan_count FROM book_loans WHERE member_id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$member_id]);
    $loan_count = $stmt->fetch()['loan_count'];
    
    if ($loan_count == 0) {
        echo "<h5>Creating Sample Loans...</h5>";
        
        // Get book IDs
        $query = "SELECT id FROM books LIMIT 3";
        $stmt = $db->query($query);
        $books = $stmt->fetchAll();
        
        foreach ($books as $index => $book) {
            $issue_date = date('Y-m-d', strtotime('-' . ($index + 5) . ' days'));
            $due_date = date('Y-m-d', strtotime('+' . (10 - $index * 5) . ' days'));
            
            // Create some overdue loans for testing
            if ($index == 0) {
                $due_date = date('Y-m-d', strtotime('-2 days')); // Overdue
                $fine = 2.00;
            } elseif ($index == 1) {
                $due_date = date('Y-m-d', strtotime('+2 days')); // Due soon
                $fine = 0;
            } else {
                $fine = 0;
            }
            
            $query = "INSERT INTO book_loans (book_id, member_id, issue_date, due_date, status, fine) 
                      VALUES (?, ?, ?, ?, 'borrowed', ?)";
            $stmt = $db->prepare($query);
            $stmt->execute([$book['id'], $member_id, $issue_date, $due_date, $fine]);
            
            echo "<p class='text-success'>✅ Created loan for book ID {$book['id']} (Due: $due_date)</p>";
        }
    } else {
        echo "<p class='text-info'>✅ Sample loans already exist ($loan_count loans found)</p>";
    }
    
    // Create sample wishlist items
    $query = "SELECT COUNT(*) as wishlist_count FROM member_wishlist WHERE member_id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$member_id]);
    $wishlist_count = $stmt->fetch()['wishlist_count'];
    
    if ($wishlist_count == 0) {
        echo "<h5>Creating Sample Wishlist...</h5>";
        
        $query = "SELECT id FROM books WHERE id NOT IN (SELECT book_id FROM book_loans WHERE member_id = ?) LIMIT 2";
        $stmt = $db->prepare($query);
        $stmt->execute([$member_id]);
        $wishlist_books = $stmt->fetchAll();
        
        foreach ($wishlist_books as $book) {
            $query = "INSERT INTO member_wishlist (member_id, book_id, added_date, priority) 
                      VALUES (?, ?, NOW(), 'medium')";
            $stmt = $db->prepare($query);
            $stmt->execute([$member_id, $book['id']]);
            echo "<p class='text-success'>✅ Added book ID {$book['id']} to wishlist</p>";
        }
    } else {
        echo "<p class='text-info'>✅ Wishlist items already exist ($wishlist_count items found)</p>";
    }
    
    echo "<div class='alert alert-success mt-4'>
            <h5><i class='bi bi-check-circle me-2'></i>Test Data Creation Complete!</h5>
            <p><strong>Test Member Credentials:</strong></p>
            <ul>
                <li><strong>Email:</strong> <EMAIL></li>
                <li><strong>Password:</strong> member123</li>
            </ul>
            <p><strong>Test Data Created:</strong></p>
            <ul>
                <li>Sample books for borrowing</li>
                <li>Active loans (including overdue and due soon)</li>
                <li>Wishlist items</li>
                <li>Fine amounts for testing payment</li>
            </ul>
          </div>";
    
    echo "<div class='mt-4 text-center'>
            <a href='test_member_dashboard_features.php' class='btn btn-primary me-2'>
                <i class='bi bi-gear me-1'></i>Go to Feature Testing
            </a>
            <a href='login.php' class='btn btn-success me-2'>
                <i class='bi bi-box-arrow-in-right me-1'></i>Login as Test Member
            </a>
            <a href='member_dashboard.php' class='btn btn-info'>
                <i class='bi bi-speedometer2 me-1'></i>View Dashboard
            </a>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>
            <h5>Error:</h5>
            <p>" . $e->getMessage() . "</p>
          </div>";
}

echo "</div></div></div></body></html>";
?>

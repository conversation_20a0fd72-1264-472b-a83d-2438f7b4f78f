<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if member is logged in
if (!isMemberLoggedIn()) {
    redirect('../member_login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get member ID from session
$member_id = $_SESSION['member_id'];

$success_message = '';
$error_message = '';

// Handle fine payment
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $payment_method = $_POST['payment_method'] ?? '';
    $amount = floatval($_POST['amount'] ?? 0);
    $payment_notes = $_POST['payment_notes'] ?? '';
    
    if (empty($payment_method)) {
        $error_message = "Please select a payment method";
    } elseif ($amount <= 0) {
        $error_message = "Please enter a valid payment amount";
    } else {
        try {
            // Start transaction
            $db->beginTransaction();
            
            // Get current total fine
            $query = "SELECT SUM(fine) as total_fine FROM book_loans WHERE member_id = :member_id AND fine > 0";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':member_id', $member_id);
            $stmt->execute();
            $current_fine = $stmt->fetch()['total_fine'] ?? 0;
            
            if ($amount > $current_fine) {
                $error_message = "Payment amount cannot exceed outstanding fine of $" . number_format($current_fine, 2);
            } else {
                // Record payment
                $query = "INSERT INTO fine_payments (member_id, amount, payment_method, payment_notes, payment_date) 
                          VALUES (:member_id, :amount, :payment_method, :payment_notes, NOW())";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':member_id', $member_id);
                $stmt->bindParam(':amount', $amount);
                $stmt->bindParam(':payment_method', $payment_method);
                $stmt->bindParam(':payment_notes', $payment_notes);
                $stmt->execute();
                
                // Update loan fines (reduce proportionally)
                $query = "SELECT id, fine FROM book_loans WHERE member_id = :member_id AND fine > 0 ORDER BY due_date ASC";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':member_id', $member_id);
                $stmt->execute();
                $loans_with_fines = $stmt->fetchAll();
                
                $remaining_payment = $amount;
                foreach ($loans_with_fines as $loan) {
                    if ($remaining_payment <= 0) break;
                    
                    $payment_for_this_loan = min($remaining_payment, $loan['fine']);
                    $new_fine = $loan['fine'] - $payment_for_this_loan;
                    
                    $query = "UPDATE book_loans SET fine = :new_fine WHERE id = :loan_id";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':new_fine', $new_fine);
                    $stmt->bindParam(':loan_id', $loan['id']);
                    $stmt->execute();
                    
                    $remaining_payment -= $payment_for_this_loan;
                }
                
                // Log activity
                logActivity($db, 'payment', "Fine payment of $" . number_format($amount, 2) . " via " . $payment_method, 'member', $member_id);
                
                // Commit transaction
                $db->commit();
                
                $success_message = "Payment of $" . number_format($amount, 2) . " has been processed successfully!";
                
                // Redirect back to dashboard after 3 seconds
                header("refresh:3;url=../member_dashboard.php");
            }
        } catch (PDOException $e) {
            $db->rollBack();
            $error_message = "Error processing payment: " . $e->getMessage();
        }
    }
}

// Get member details
$query = "SELECT * FROM members WHERE id = :member_id";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$member = $stmt->fetch();

// Get current total fine
$query = "SELECT SUM(fine) as total_fine FROM book_loans WHERE member_id = :member_id AND fine > 0";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$total_fine = $stmt->fetch()['total_fine'] ?? 0;

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php include '../includes/head.php'; ?>
    <title>Pay Fine - Library Management System</title>
    <style>
        .payment-card {
            max-width: 600px;
            margin: 0 auto;
        }
        .payment-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container mt-4">
        <div class="payment-card">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-credit-card me-2"></i>Pay Outstanding Fines
                    </h4>
                </div>
                <div class="card-body">
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i><?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i><?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <div class="payment-summary">
                        <h5><i class="bi bi-person-circle me-2"></i><?php echo h($member['first_name'] . ' ' . $member['last_name']); ?></h5>
                        <h3><i class="bi bi-currency-dollar me-2"></i><?php echo number_format($total_fine, 2); ?></h3>
                        <p class="mb-0">Total Outstanding Fine</p>
                    </div>

                    <?php if ($total_fine > 0): ?>
                        <form action="" method="post">
                            <div class="mb-3">
                                <label for="payment_method" class="form-label">Payment Method</label>
                                <select class="form-select" id="payment_method" name="payment_method" required>
                                    <option value="">Select Payment Method</option>
                                    <option value="cash">Cash</option>
                                    <option value="card">Credit/Debit Card</option>
                                    <option value="online">Online Payment</option>
                                    <option value="bank_transfer">Bank Transfer</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="amount" class="form-label">Amount to Pay</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="amount" name="amount" 
                                           step="0.01" max="<?php echo $total_fine; ?>" 
                                           value="<?php echo $total_fine; ?>" required>
                                </div>
                                <div class="form-text">Maximum amount: $<?php echo number_format($total_fine, 2); ?></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="payment_notes" class="form-label">Notes (Optional)</label>
                                <textarea class="form-control" id="payment_notes" name="payment_notes" rows="3" 
                                          placeholder="Add any additional notes about this payment..."></textarea>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-credit-card me-2"></i>Process Payment
                                </button>
                                <a href="../member_dashboard.php" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
                                </a>
                            </div>
                        </form>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="bi bi-check-circle display-1 text-success"></i>
                            <h4 class="mt-3">No Outstanding Fines</h4>
                            <p class="text-muted">You don't have any outstanding fines to pay.</p>
                            <a href="../member_dashboard.php" class="btn btn-primary">
                                <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

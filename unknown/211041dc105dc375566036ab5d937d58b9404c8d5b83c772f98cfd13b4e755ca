<?php
/**
 * Fix Content File Column Error
 * This script fixes the PDOException error related to the missing 'content_file' column
 */

// Start output buffering for clean display
ob_start();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Content File Error - LMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="bi bi-tools me-2"></i>Fix Content File Database Error</h4>
                    </div>
                    <div class="card-body">

<?php
// Include database configuration
require_once 'config/database.php';

try {
    echo "<h5><i class='bi bi-database me-2'></i>Connecting to Database...</h5>";
    
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Failed to connect to database");
    }
    
    echo "<p class='success'>✅ Database connected successfully</p>";
    
    // Check if the problematic content_file column exists
    echo "<h5><i class='bi bi-search me-2'></i>Checking for problematic columns...</h5>";
    
    $tables_to_check = ['books', 'members', 'book_loans', 'users'];
    
    foreach ($tables_to_check as $table) {
        try {
            echo "<h6>Checking table: <code>$table</code></h6>";
            
            // Get table structure
            $query = "DESCRIBE $table";
            $stmt = $db->query($query);
            $columns = $stmt->fetchAll();
            
            $has_content_file = false;
            foreach ($columns as $column) {
                if ($column['Field'] == 'content_file') {
                    $has_content_file = true;
                    break;
                }
            }
            
            if ($has_content_file) {
                echo "<p class='warning'>⚠️ Found problematic 'content_file' column in $table table</p>";
                
                // Remove the problematic column
                $query = "ALTER TABLE $table DROP COLUMN content_file";
                $db->exec($query);
                echo "<p class='success'>✅ Removed 'content_file' column from $table table</p>";
            } else {
                echo "<p class='success'>✅ No 'content_file' column found in $table table</p>";
            }
            
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), "doesn't exist") !== false) {
                echo "<p class='info'>ℹ️ Table $table doesn't exist (this is normal for some tables)</p>";
            } else {
                echo "<p class='error'>❌ Error checking $table: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // Verify books table structure
    echo "<h5><i class='bi bi-check-circle me-2'></i>Verifying Books Table Structure...</h5>";
    
    $query = "CREATE TABLE IF NOT EXISTS books (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        author VARCHAR(255) NOT NULL,
        isbn VARCHAR(20) UNIQUE,
        category VARCHAR(100),
        description TEXT,
        publication_year INT,
        total_quantity INT DEFAULT 1,
        available_quantity INT DEFAULT 1,
        cover_image VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $db->exec($query);
    echo "<p class='success'>✅ Books table structure verified and corrected</p>";
    
    // Test a simple query to make sure everything works
    echo "<h5><i class='bi bi-play-circle me-2'></i>Testing Database Queries...</h5>";
    
    $test_queries = [
        'books' => "SELECT COUNT(*) as count FROM books",
        'members' => "SELECT COUNT(*) as count FROM members",
        'book_loans' => "SELECT COUNT(*) as count FROM book_loans"
    ];
    
    foreach ($test_queries as $table => $query) {
        try {
            $stmt = $db->prepare($query);
            $stmt->execute();
            $result = $stmt->fetch();
            $count = $result['count'];
            echo "<p class='success'>✅ $table table: $count records found</p>";
        } catch (PDOException $e) {
            echo "<p class='error'>❌ Error testing $table: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<div class='alert alert-success mt-4'>
            <h5><i class='bi bi-check-circle-fill me-2'></i>Database Fixed Successfully!</h5>
            <p>The content_file column error has been resolved. You can now access the member dashboard.</p>
          </div>";
    
    echo "<div class='mt-4 text-center'>
            <a href='safe_member_dashboard.php' class='btn btn-success btn-lg me-2'>
                <i class='bi bi-speedometer2 me-2'></i>Safe Member Dashboard
            </a>
            <a href='member_dashboard.php' class='btn btn-primary btn-lg'>
                <i class='bi bi-credit-card me-2'></i>Enhanced Dashboard (with Payment)
            </a>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>
            <h5><i class='bi bi-exclamation-triangle-fill me-2'></i>Database Error:</h5>
            <p><strong>Error:</strong> " . $e->getMessage() . "</p>
            <p><strong>Possible solutions:</strong></p>
            <ul>
                <li>Make sure MySQL/XAMPP is running</li>
                <li>Check if database 'lms_db' exists</li>
                <li>Verify database credentials in config/database.php</li>
            </ul>
          </div>";
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
            <h5><i class='bi bi-exclamation-triangle-fill me-2'></i>General Error:</h5>
            <p>" . $e->getMessage() . "</p>
          </div>";
}

?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

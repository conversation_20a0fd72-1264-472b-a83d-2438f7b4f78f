<?php
session_start();
require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

$setup_results = [];
$test_member_created = false;

// Handle setup action
if (isset($_POST['run_complete_setup'])) {
    try {
        // 1. Create database tables
        $sql_statements = [
            "CREATE TABLE IF NOT EXISTS fine_payments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                member_id INT NOT NULL,
                amount DECIMAL(10, 2) NOT NULL,
                payment_method VARCHAR(50) NOT NULL,
                payment_notes TEXT,
                payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
            )",
            
            "CREATE TABLE IF NOT EXISTS member_wishlist (
                id INT AUTO_INCREMENT PRIMARY KEY,
                member_id INT NOT NULL,
                book_id INT NOT NULL,
                added_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
                <PERSON>OR<PERSON>G<PERSON> KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
                FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
                UNIQUE KEY unique_member_book (member_id, book_id)
            )",
            
            "CREATE TABLE IF NOT EXISTS book_reviews (
                id INT AUTO_INCREMENT PRIMARY KEY,
                book_id INT NOT NULL,
                member_id INT NOT NULL,
                rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
                review_text TEXT,
                review_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
                FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
                UNIQUE KEY unique_member_book_review (member_id, book_id)
            )",
            
            "ALTER TABLE book_loans 
             ADD COLUMN IF NOT EXISTS renewal_count INT DEFAULT 0,
             ADD COLUMN IF NOT EXISTS renewal_date TIMESTAMP NULL"
        ];
        
        foreach ($sql_statements as $sql) {
            try {
                $db->exec($sql);
                $setup_results[] = "✅ Database table created/updated successfully";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'already exists') === false) {
                    $setup_results[] = "⚠️ " . $e->getMessage();
                }
            }
        }
        
        // 2. Create sample books
        $books = [
            ['To Kill a Mockingbird', 'Harper Lee', '9780061120084', 'Fiction'],
            ['1984', 'George Orwell', '9780451524935', 'Fiction'],
            ['Pride and Prejudice', 'Jane Austen', '9780141439518', 'Romance'],
            ['The Great Gatsby', 'F. Scott Fitzgerald', '9780743273565', 'Fiction'],
            ['Harry Potter', 'J.K. Rowling', '9780439708180', 'Fantasy']
        ];
        
        foreach ($books as $book) {
            $query = "INSERT IGNORE INTO books (title, author, isbn, category, total_quantity, available_quantity, publication_year) 
                      VALUES (?, ?, ?, ?, 3, 3, 2020)";
            $stmt = $db->prepare($query);
            $stmt->execute($book);
        }
        $setup_results[] = "✅ Sample books created";
        
        // 3. Create test member
        $query = "SELECT * FROM members WHERE email = '<EMAIL>'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        if ($stmt->rowCount() == 0) {
            $query = "INSERT INTO members (first_name, last_name, email, password, phone, address, membership_date, membership_status) 
                      VALUES ('Test', 'Member', '<EMAIL>', ?, '************', '123 Test Street', NOW(), 'active')";
            $stmt = $db->prepare($query);
            $password = password_hash('member123', PASSWORD_DEFAULT);
            $stmt->execute([$password]);
            $setup_results[] = "✅ Test member created (<EMAIL> / member123)";
            $test_member_created = true;
        } else {
            $setup_results[] = "✅ Test member already exists";
        }
        
        // 4. Get member ID and create sample data
        $query = "SELECT id FROM members WHERE email = '<EMAIL>'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $member = $stmt->fetch();
        $member_id = $member['id'];
        
        // Create sample loans
        $query = "SELECT id FROM books LIMIT 3";
        $stmt = $db->query($query);
        $books = $stmt->fetchAll();
        
        foreach ($books as $index => $book) {
            $issue_date = date('Y-m-d', strtotime('-' . ($index + 5) . ' days'));
            $due_date = date('Y-m-d', strtotime('+' . (10 - $index * 5) . ' days'));
            $fine = ($index == 0) ? 2.00 : 0; // First book has fine
            
            $query = "INSERT IGNORE INTO book_loans (book_id, member_id, issue_date, due_date, status, fine) 
                      VALUES (?, ?, ?, ?, 'borrowed', ?)";
            $stmt = $db->prepare($query);
            $stmt->execute([$book['id'], $member_id, $issue_date, $due_date, $fine]);
        }
        $setup_results[] = "✅ Sample loans created (including overdue with fine)";
        
        // Create wishlist items
        $query = "SELECT id FROM books WHERE id NOT IN (SELECT book_id FROM book_loans WHERE member_id = ?) LIMIT 2";
        $stmt = $db->prepare($query);
        $stmt->execute([$member_id]);
        $wishlist_books = $stmt->fetchAll();
        
        foreach ($wishlist_books as $book) {
            $query = "INSERT IGNORE INTO member_wishlist (member_id, book_id, added_date, priority) 
                      VALUES (?, ?, NOW(), 'medium')";
            $stmt = $db->prepare($query);
            $stmt->execute([$member_id, $book['id']]);
        }
        $setup_results[] = "✅ Sample wishlist items created";
        
        $setup_results[] = "🎉 COMPLETE SETUP FINISHED! All enhanced features are ready!";
        
    } catch (Exception $e) {
        $setup_results[] = "❌ Error: " . $e->getMessage();
    }
}

// Handle auto-login
if (isset($_POST['auto_login'])) {
    try {
        $query = "SELECT * FROM members WHERE email = '<EMAIL>'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $member = $stmt->fetch();
        
        if ($member) {
            $_SESSION['member_id'] = $member['id'];
            $_SESSION['member_name'] = $member['first_name'] . ' ' . $member['last_name'];
            $_SESSION['member_email'] = $member['email'];
            
            header('Location: member_dashboard.php');
            exit;
        }
    } catch (Exception $e) {
        $setup_results[] = "❌ Login error: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Setup & Test - Enhanced Member Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .setup-container { max-width: 900px; margin: 20px auto; }
        .feature-preview { border-left: 4px solid #007bff; padding-left: 15px; margin: 10px 0; }
        .result-success { color: #28a745; }
        .result-warning { color: #ffc107; }
        .result-error { color: #dc3545; }
    </style>
</head>
<body class="bg-light">
    <div class="setup-container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h2 class="mb-0">
                    <i class="bi bi-rocket-takeoff me-2"></i>Complete Setup & Test - Enhanced Member Dashboard
                </h2>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h5><i class="bi bi-info-circle me-2"></i>Kumusta! Ania ang tanan nga enhancements:</h5>
                    <p>Ang member dashboard kay na-enhance na jud with modern features. Kini nga script mag-setup sa tanan nga kinahanglan.</p>
                </div>

                <?php if (!empty($setup_results)): ?>
                    <div class="alert alert-secondary">
                        <h6>Setup Results:</h6>
                        <?php foreach ($setup_results as $result): ?>
                            <div class="<?php echo strpos($result, '✅') !== false ? 'result-success' : (strpos($result, '⚠️') !== false ? 'result-warning' : 'result-error'); ?>">
                                <?php echo $result; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <form method="post" class="d-grid">
                            <button type="submit" name="run_complete_setup" class="btn btn-warning btn-lg">
                                <i class="bi bi-gear-fill me-2"></i>Run Complete Setup
                            </button>
                        </form>
                        <small class="text-muted">Creates all database tables, sample data, and test member</small>
                    </div>
                    <div class="col-md-6">
                        <form method="post" class="d-grid">
                            <button type="submit" name="auto_login" class="btn btn-success btn-lg">
                                <i class="bi bi-box-arrow-in-right me-2"></i>Login & View Dashboard
                            </button>
                        </form>
                        <small class="text-muted">Auto-login as test member and view enhanced dashboard</small>
                    </div>
                </div>

                <h5><i class="bi bi-star-fill me-2"></i>Enhanced Features nga makita nimo:</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="feature-preview">
                            <h6>🔍 Enhanced Navigation</h6>
                            <ul>
                                <li>Quick search bar</li>
                                <li>Borrow Books link with icon</li>
                                <li>Wishlist with count badge</li>
                                <li>Mobile responsive menu</li>
                            </ul>
                        </div>

                        <div class="feature-preview">
                            <h6>🚨 Smart Alerts</h6>
                            <ul>
                                <li>Overdue books alert (red)</li>
                                <li>Due soon alert (yellow)</li>
                                <li>Fine payment alert (blue)</li>
                                <li>Dismissible alerts</li>
                            </ul>
                        </div>

                        <div class="feature-preview">
                            <h6>📊 Enhanced Statistics</h6>
                            <ul>
                                <li>Current loans with overdue count</li>
                                <li>Books read this year</li>
                                <li>Wishlist items counter</li>
                                <li>Reading progress display</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="feature-preview">
                            <h6>⚡ Quick Actions Panel</h6>
                            <ul>
                                <li>Borrow Books button</li>
                                <li>Return Books with count</li>
                                <li>Browse Catalog</li>
                                <li>My Wishlist</li>
                            </ul>
                        </div>

                        <div class="feature-preview">
                            <h6>💳 Fine Payment System</h6>
                            <ul>
                                <li>Multiple payment methods</li>
                                <li>Partial payment support</li>
                                <li>Payment history tracking</li>
                                <li>Modal interface</li>
                            </ul>
                        </div>

                        <div class="feature-preview">
                            <h6>🔄 Book Renewal</h6>
                            <ul>
                                <li>One-click renewal</li>
                                <li>Renewal limit tracking</li>
                                <li>14-day extension</li>
                                <li>Renewal history</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="alert alert-success mt-4">
                    <h6><i class="bi bi-check-circle me-2"></i>Test Member Credentials:</h6>
                    <p><strong>Email:</strong> <EMAIL><br>
                       <strong>Password:</strong> member123</p>
                </div>

                <div class="text-center mt-4">
                    <a href="member_dashboard.php" class="btn btn-primary me-2">
                        <i class="bi bi-speedometer2 me-1"></i>View Enhanced Dashboard
                    </a>
                    <a href="debug_dashboard.php" class="btn btn-info me-2">
                        <i class="bi bi-bug me-1"></i>Debug Info
                    </a>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="bi bi-house me-1"></i>Back to Home
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

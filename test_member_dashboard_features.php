<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

$test_results = [];
$test_member = null;

// Function to create test member if needed
function createTestMember($db) {
    try {
        // Check if test member exists
        $query = "SELECT * FROM members WHERE email = '<EMAIL>'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        if ($stmt->rowCount() == 0) {
            // Create test member
            $query = "INSERT INTO members (first_name, last_name, email, password, phone, address, membership_date, membership_status) 
                      VALUES ('Test', 'Member', '<EMAIL>', :password, '************', '123 Test Street', NOW(), 'active')";
            $stmt = $db->prepare($query);
            $password = password_hash('member123', PASSWORD_DEFAULT);
            $stmt->bindParam(':password', $password);
            $stmt->execute();
            
            return ['success' => true, 'message' => 'Test member created successfully'];
        } else {
            return ['success' => true, 'message' => 'Test member already exists'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

// Function to create sample loan data
function createSampleLoans($db, $member_id) {
    try {
        // Check if member has loans
        $query = "SELECT COUNT(*) as loan_count FROM book_loans WHERE member_id = :member_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':member_id', $member_id);
        $stmt->execute();
        $loan_count = $stmt->fetch()['loan_count'];
        
        if ($loan_count == 0) {
            // Get some books to create loans
            $query = "SELECT id FROM books LIMIT 3";
            $stmt = $db->query($query);
            $books = $stmt->fetchAll();
            
            if (count($books) > 0) {
                foreach ($books as $index => $book) {
                    $issue_date = date('Y-m-d', strtotime('-' . ($index + 1) . ' days'));
                    $due_date = date('Y-m-d', strtotime('+' . (14 - $index) . ' days'));
                    
                    $query = "INSERT INTO book_loans (book_id, member_id, issue_date, due_date, status) 
                              VALUES (:book_id, :member_id, :issue_date, :due_date, 'borrowed')";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':book_id', $book['id']);
                    $stmt->bindParam(':member_id', $member_id);
                    $stmt->bindParam(':issue_date', $issue_date);
                    $stmt->bindParam(':due_date', $due_date);
                    $stmt->execute();
                }
                return ['success' => true, 'message' => 'Sample loans created'];
            }
        }
        return ['success' => true, 'message' => 'Loans already exist or no books available'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error creating loans: ' . $e->getMessage()];
    }
}

// Handle test actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['setup_test_data'])) {
        $test_results['member_creation'] = createTestMember($db);
        
        // Get the test member
        $query = "SELECT * FROM members WHERE email = '<EMAIL>'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $test_member = $stmt->fetch();
        
        if ($test_member) {
            $test_results['sample_loans'] = createSampleLoans($db, $test_member['id']);
        }
    }
    
    if (isset($_POST['login_test_member'])) {
        // Login as test member
        $query = "SELECT * FROM members WHERE email = '<EMAIL>'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $member = $stmt->fetch();
        
        if ($member) {
            $_SESSION['member_id'] = $member['id'];
            $_SESSION['member_name'] = $member['first_name'] . ' ' . $member['last_name'];
            $_SESSION['member_email'] = $member['email'];
            
            header('Location: member_dashboard.php');
            exit;
        }
    }
}

// Get test member info
$query = "SELECT * FROM members WHERE email = '<EMAIL>'";
$stmt = $db->prepare($query);
$stmt->execute();
$test_member = $stmt->fetch();

// Check database tables
$tables_to_check = ['fine_payments', 'member_wishlist', 'book_reviews'];
$table_status = [];

foreach ($tables_to_check as $table) {
    try {
        $query = "SHOW TABLES LIKE '$table'";
        $stmt = $db->query($query);
        $table_status[$table] = $stmt->rowCount() > 0;
    } catch (Exception $e) {
        $table_status[$table] = false;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Member Dashboard Features</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .test-container { max-width: 1000px; margin: 20px auto; }
        .test-card { margin-bottom: 20px; }
        .status-success { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .feature-test { border-left: 4px solid #007bff; padding-left: 15px; margin-bottom: 15px; }
    </style>
</head>
<body class="bg-light">
    <div class="test-container">
        <div class="card test-card">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0">
                    <i class="bi bi-gear-fill me-2"></i>Member Dashboard Features Testing
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>Testing Guide:</strong> This page helps you systematically test all the enhanced member dashboard features.
                </div>

                <!-- Database Status -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5><i class="bi bi-database me-2"></i>Database Tables Status</h5>
                        <?php foreach ($table_status as $table => $exists): ?>
                            <div class="<?php echo $exists ? 'status-success' : 'status-error'; ?>">
                                <?php echo $exists ? '✅' : '❌'; ?> <?php echo $table; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="bi bi-person me-2"></i>Test Member Status</h5>
                        <?php if ($test_member): ?>
                            <div class="status-success">✅ Test member exists</div>
                            <div><strong>Email:</strong> <EMAIL></div>
                            <div><strong>Password:</strong> member123</div>
                        <?php else: ?>
                            <div class="status-error">❌ Test member not found</div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Test Results -->
                <?php if (!empty($test_results)): ?>
                    <div class="alert alert-secondary">
                        <h6>Test Results:</h6>
                        <?php foreach ($test_results as $test => $result): ?>
                            <div class="<?php echo $result['success'] ? 'status-success' : 'status-error'; ?>">
                                <?php echo $result['success'] ? '✅' : '❌'; ?> <?php echo ucfirst(str_replace('_', ' ', $test)); ?>: <?php echo $result['message']; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <!-- Setup Actions -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <form method="post" class="d-grid">
                            <button type="submit" name="setup_test_data" class="btn btn-warning">
                                <i class="bi bi-tools me-2"></i>Setup Test Data
                            </button>
                        </form>
                    </div>
                    <div class="col-md-6">
                        <form method="post" class="d-grid">
                            <button type="submit" name="login_test_member" class="btn btn-success" <?php echo !$test_member ? 'disabled' : ''; ?>>
                                <i class="bi bi-box-arrow-in-right me-2"></i>Login as Test Member
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Feature Testing Checklist -->
                <h5><i class="bi bi-list-check me-2"></i>Feature Testing Checklist</h5>
                
                <div class="feature-test">
                    <h6>1. Enhanced Navigation</h6>
                    <ul>
                        <li>✓ Quick search bar in navigation</li>
                        <li>✓ Borrow Books link with icon</li>
                        <li>✓ My Loans quick access</li>
                        <li>✓ Wishlist with item count badge</li>
                        <li>✓ Mobile responsive navigation</li>
                    </ul>
                </div>

                <div class="feature-test">
                    <h6>2. Smart Alerts</h6>
                    <ul>
                        <li>✓ Overdue books alert (red)</li>
                        <li>✓ Due soon alert (yellow)</li>
                        <li>✓ Fine payment alert (blue)</li>
                        <li>✓ Dismissible alerts</li>
                    </ul>
                </div>

                <div class="feature-test">
                    <h6>3. Enhanced Statistics</h6>
                    <ul>
                        <li>✓ Current loans with overdue count</li>
                        <li>✓ Books read this year</li>
                        <li>✓ Wishlist items counter</li>
                        <li>✓ Favorite genre display</li>
                    </ul>
                </div>

                <div class="feature-test">
                    <h6>4. Quick Actions Panel</h6>
                    <ul>
                        <li>✓ Borrow Books button</li>
                        <li>✓ Return Books with count badge</li>
                        <li>✓ Browse Catalog button</li>
                        <li>✓ My Wishlist with count</li>
                    </ul>
                </div>

                <div class="feature-test">
                    <h6>5. Enhanced Current Loans</h6>
                    <ul>
                        <li>✓ Color-coded table rows</li>
                        <li>✓ Book cover images</li>
                        <li>✓ Quick action buttons (return/renew)</li>
                        <li>✓ Status badges</li>
                        <li>✓ Enhanced empty state</li>
                    </ul>
                </div>

                <div class="feature-test">
                    <h6>6. Fine Payment System</h6>
                    <ul>
                        <li>✓ Multiple payment methods</li>
                        <li>✓ Partial payment support</li>
                        <li>✓ Payment modal interface</li>
                        <li>✓ Payment history tracking</li>
                    </ul>
                </div>

                <div class="feature-test">
                    <h6>7. Book Renewal</h6>
                    <ul>
                        <li>✓ One-click renewal from table</li>
                        <li>✓ Renewal limit enforcement (max 2)</li>
                        <li>✓ Reservation conflict checking</li>
                        <li>✓ 14-day extension calculation</li>
                    </ul>
                </div>

                <!-- Quick Links -->
                <div class="mt-4">
                    <h5><i class="bi bi-link me-2"></i>Quick Test Links</h5>
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="member_dashboard.php" class="btn btn-primary w-100">
                                <i class="bi bi-speedometer2 me-1"></i>Dashboard
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="member/self_checkout.php" class="btn btn-success w-100">
                                <i class="bi bi-plus-circle me-1"></i>Borrow Books
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="member/return_book.php" class="btn btn-warning w-100">
                                <i class="bi bi-arrow-return-left me-1"></i>Return Books
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="member/pay_fine.php" class="btn btn-info w-100">
                                <i class="bi bi-credit-card me-1"></i>Pay Fines
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="alert alert-success mt-4">
                    <h6><i class="bi bi-lightbulb me-2"></i>Testing Instructions:</h6>
                    <ol>
                        <li>Click "Setup Test Data" to create test member and sample loans</li>
                        <li>Click "Login as Test Member" to access the dashboard</li>
                        <li>Test each feature systematically using the checklist above</li>
                        <li>Use the quick test links to navigate between features</li>
                        <li>Check mobile responsiveness by resizing your browser</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

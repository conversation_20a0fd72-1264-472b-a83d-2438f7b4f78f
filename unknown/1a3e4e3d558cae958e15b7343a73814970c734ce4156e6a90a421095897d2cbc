<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Access Enhanced Member Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .access-container {
            max-width: 800px;
            margin: 50px auto;
            text-align: center;
        }
        .url-box {
            background: #f8f9fa;
            border: 2px solid #007bff;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 1.2rem;
        }
        .step-card {
            margin: 15px 0;
            text-align: left;
        }
    </style>
</head>
<body class="bg-light">
    <div class="access-container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h2><i class="bi bi-speedometer2 me-2"></i>Access Enhanced Member Dashboard</h2>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h5><i class="bi bi-exclamation-triangle me-2"></i>Important!</h5>
                    <p>Para makita ang enhanced member dashboard, kinahanglan nimo i-access ang correct URL:</p>
                </div>

                <div class="url-box">
                    <strong>Correct URL:</strong><br>
                    <a href="http://localhost:8080/member_dashboard.php" target="_blank">
                        http://localhost:8080/member_dashboard.php
                    </a>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card step-card">
                            <div class="card-header bg-success text-white">
                                <h6><i class="bi bi-1-circle me-2"></i>Step 1: Setup</h6>
                            </div>
                            <div class="card-body">
                                <p>Run the complete setup first:</p>
                                <a href="http://localhost:8080/complete_setup_and_test.php" class="btn btn-warning w-100" target="_blank">
                                    <i class="bi bi-gear me-1"></i>Complete Setup
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card step-card">
                            <div class="card-header bg-info text-white">
                                <h6><i class="bi bi-2-circle me-2"></i>Step 2: Login</h6>
                            </div>
                            <div class="card-body">
                                <p>Quick login as test member:</p>
                                <a href="http://localhost:8080/quick_member_login.php" class="btn btn-success w-100" target="_blank">
                                    <i class="bi bi-box-arrow-in-right me-1"></i>Quick Login
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info mt-4">
                    <h6><i class="bi bi-info-circle me-2"></i>Test Member Credentials:</h6>
                    <p><strong>Email:</strong> <EMAIL><br>
                       <strong>Password:</strong> member123</p>
                </div>

                <div class="mt-4">
                    <h5>Enhanced Features nga makita nimo:</h5>
                    <div class="row text-start">
                        <div class="col-md-6">
                            <ul>
                                <li>🔍 Enhanced navigation with search</li>
                                <li>🚨 Smart alerts (overdue, due soon, fines)</li>
                                <li>📊 Reading statistics cards</li>
                                <li>⚡ Quick actions panel</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul>
                                <li>📚 Enhanced loans table</li>
                                <li>💳 Fine payment system</li>
                                <li>🔄 Book renewal feature</li>
                                <li>📱 Mobile responsive design</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <a href="http://localhost:8080/member_dashboard.php" class="btn btn-primary btn-lg me-2" target="_blank">
                        <i class="bi bi-speedometer2 me-2"></i>View Enhanced Dashboard
                    </a>
                    <a href="http://localhost:8080/debug_dashboard.php" class="btn btn-outline-info" target="_blank">
                        <i class="bi bi-bug me-2"></i>Debug Info
                    </a>
                </div>

                <div class="alert alert-success mt-4">
                    <h6><i class="bi bi-check-circle me-2"></i>Reminder:</h6>
                    <p>Always use <strong>localhost:8080</strong> para sa correct port. Ang XAMPP default kay different port.</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

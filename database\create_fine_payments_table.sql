-- Create fine_payments table for tracking member fine payments
CREATE TABLE IF NOT EXISTS fine_payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    payment_notes TEXT,
    payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_by INT NULL,
    status ENUM('pending', 'completed', 'failed') DEFAULT 'completed',
    transaction_id VARCHAR(100) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON>OREIG<PERSON> KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_member_id (member_id),
    INDEX idx_payment_date (payment_date),
    INDEX idx_status (status)
);

-- Add renewal_count and renewal_date columns to book_loans table if they don't exist
ALTER TABLE book_loans 
ADD COLUMN IF NOT EXISTS renewal_count INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS renewal_date TIMESTAMP NULL;

-- Create member_wishlist table if it doesn't exist
CREATE TABLE IF NOT EXISTS member_wishlist (
    id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT NOT NULL,
    book_id INT NOT NULL,
    added_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    UNIQUE KEY unique_member_book (member_id, book_id),
    INDEX idx_member_id (member_id),
    INDEX idx_book_id (book_id),
    INDEX idx_added_date (added_date)
);

-- Create book_reviews table if it doesn't exist
CREATE TABLE IF NOT EXISTS book_reviews (
    id INT AUTO_INCREMENT PRIMARY KEY,
    book_id INT NOT NULL,
    member_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    review_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_approved BOOLEAN DEFAULT TRUE,
    helpful_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    UNIQUE KEY unique_member_book_review (member_id, book_id),
    INDEX idx_book_id (book_id),
    INDEX idx_member_id (member_id),
    INDEX idx_rating (rating),
    INDEX idx_review_date (review_date)
);

-- Insert sample data for testing (optional)
-- You can run this separately if you want test data

-- Sample fine payments
INSERT IGNORE INTO fine_payments (member_id, amount, payment_method, payment_notes) VALUES
(1, 5.00, 'cash', 'Overdue fine payment'),
(2, 2.50, 'card', 'Late return fee'),
(3, 7.50, 'online', 'Multiple overdue books');

-- Sample wishlist items
INSERT IGNORE INTO member_wishlist (member_id, book_id, notes, priority) VALUES
(1, 1, 'Want to read this classic', 'high'),
(1, 2, 'Recommended by friend', 'medium'),
(2, 3, 'For book club', 'high'),
(3, 1, 'Research purposes', 'low');

-- Sample book reviews
INSERT IGNORE INTO book_reviews (book_id, member_id, rating, review_text) VALUES
(1, 1, 5, 'Excellent book! Highly recommended for anyone interested in classic literature.'),
(2, 1, 4, 'Good read, though a bit slow in the middle. Overall worth the time.'),
(1, 2, 5, 'One of the best books I have ever read. The character development is outstanding.'),
(3, 2, 3, 'Average book. Some interesting parts but not very engaging overall.');

<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html>
<head>
    <title>Debug Dashboard</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body class='bg-light'>
<div class='container mt-4'>
    <h1>🔍 Dashboard Debug Information</h1>";

// Check session
echo "<div class='debug-section'>
        <h3>1. Session Status</h3>";
if (isset($_SESSION['member_id'])) {
    echo "<p class='success'>✅ Member logged in: ID = {$_SESSION['member_id']}, Name = {$_SESSION['member_name']}</p>";
} else {
    echo "<p class='error'>❌ No member session found</p>";
    echo "<p><a href='quick_member_login.php' class='btn btn-primary'>Quick Login</a></p>";
}
echo "</div>";

// Check database tables
echo "<div class='debug-section'>
        <h3>2. Database Tables</h3>";

$tables_to_check = [
    'members' => 'Members table',
    'books' => 'Books table', 
    'book_loans' => 'Book loans table',
    'member_wishlist' => 'Member wishlist table',
    'fine_payments' => 'Fine payments table',
    'book_reviews' => 'Book reviews table'
];

foreach ($tables_to_check as $table => $description) {
    try {
        $query = "SHOW TABLES LIKE '$table'";
        $stmt = $db->query($query);
        if ($stmt->rowCount() > 0) {
            // Count records
            $count_query = "SELECT COUNT(*) as count FROM $table";
            $count_stmt = $db->query($count_query);
            $count = $count_stmt->fetch()['count'];
            echo "<p class='success'>✅ $description exists ($count records)</p>";
        } else {
            echo "<p class='error'>❌ $description missing</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error checking $description: " . $e->getMessage() . "</p>";
    }
}
echo "</div>";

// Check if member has data
if (isset($_SESSION['member_id'])) {
    $member_id = $_SESSION['member_id'];
    
    echo "<div class='debug-section'>
            <h3>3. Member Data</h3>";
    
    try {
        // Check loans
        $query = "SELECT COUNT(*) as count FROM book_loans WHERE member_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$member_id]);
        $loan_count = $stmt->fetch()['count'];
        echo "<p class='success'>✅ Member has $loan_count loans</p>";
        
        // Check wishlist
        $query = "SELECT COUNT(*) as count FROM member_wishlist WHERE member_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$member_id]);
        $wishlist_count = $stmt->fetch()['count'];
        echo "<p class='success'>✅ Member has $wishlist_count wishlist items</p>";
        
        // Check fines
        $query = "SELECT SUM(fine) as total_fine FROM book_loans WHERE member_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$member_id]);
        $total_fine = $stmt->fetch()['total_fine'] ?? 0;
        echo "<p class='success'>✅ Member has $" . number_format($total_fine, 2) . " in fines</p>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error checking member data: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
}

// Check if enhanced features are working
echo "<div class='debug-section'>
        <h3>4. Enhanced Features Test</h3>";

// Test if member_dashboard.php has the enhancements
$dashboard_content = file_get_contents('member_dashboard.php');
$features_to_check = [
    'Quick Actions' => 'Quick Actions',
    'Enhanced Statistics Cards' => 'Enhanced Statistics Cards', 
    'Reading Progress' => 'Reading Progress',
    'Smart Alert System' => 'Enhanced Alert System',
    'Wishlist' => 'wishlist_count',
    'Fine Payment' => 'payFineModal'
];

foreach ($features_to_check as $feature => $search_text) {
    if (strpos($dashboard_content, $search_text) !== false) {
        echo "<p class='success'>✅ $feature code found in dashboard</p>";
    } else {
        echo "<p class='error'>❌ $feature code missing from dashboard</p>";
    }
}

echo "</div>";

// Show current dashboard URL and links
echo "<div class='debug-section'>
        <h3>5. Quick Links</h3>
        <p><a href='member_dashboard.php' class='btn btn-primary'>View Member Dashboard</a></p>
        <p><a href='setup_member_dashboard_enhancements.php' class='btn btn-warning'>Run Setup Script</a></p>
        <p><a href='create_test_data.php' class='btn btn-info'>Create Test Data</a></p>
      </div>";

// Show what the dashboard should look like
echo "<div class='debug-section'>
        <h3>6. Expected Dashboard Features</h3>
        <ul>
            <li>✅ Enhanced navigation with search bar and wishlist badge</li>
            <li>✅ Smart alerts for overdue books, due soon, and fines</li>
            <li>✅ Statistics cards showing loans, wishlist, books read</li>
            <li>✅ Quick actions panel with 4 buttons</li>
            <li>✅ Reading progress card</li>
            <li>✅ Enhanced current loans table with action buttons</li>
            <li>✅ Fine payment modal</li>
            <li>✅ Book renewal functionality</li>
        </ul>
      </div>";

echo "</div></body></html>";
?>

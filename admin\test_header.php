<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../login.php');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Header Test - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <style>
        /* Force dark header */
        .navbar {
            background-color: #212529 !important;
            background: #212529 !important;
        }
        
        .navbar-brand {
            color: #ffffff !important;
        }
        
        .nav-link {
            color: #ffffff !important;
        }
        
        .form-control-dark {
            background-color: rgba(255, 255, 255, 0.1) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            color: #ffffff !important;
        }
        
        .btn-outline-light {
            border-color: rgba(255, 255, 255, 0.2) !important;
            color: #ffffff !important;
        }
    </style>
</head>
<body>
    <header class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="#">Library MS Admin</a>
        <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="w-100">
            <form class="d-none d-md-flex w-50 mx-auto" action="../search.php" method="get">
                <input class="form-control form-control-dark" type="text" name="q" placeholder="Search books, members..." aria-label="Search" required>
                <button class="btn btn-outline-light ms-2" type="submit"><i class="bi bi-search"></i></button>
            </form>
        </div>
        <div class="navbar-nav">
            <div class="nav-item text-nowrap d-flex align-items-center">
                <div class="nav-item me-2">
                    <button class="nav-link position-relative border-0 bg-transparent" type="button">
                        <span class="bell-icon">
                            <i class="bi bi-bell fs-5 text-white"></i>
                        </span>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                            3
                        </span>
                    </button>
                </div>
                <span class="nav-link px-3 text-white">Welcome, Admin</span>
                <a class="btn btn-danger btn-sm mx-2" href="../logout.php">
                    <i class="bi bi-box-arrow-right me-1"></i>Sign out
                </a>
            </div>
        </div>
    </header>

    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0"><i class="bi bi-check-circle me-2"></i>Header Test</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle me-2"></i>Header Status</h6>
                            <p class="mb-1">This page shows how the header should look with dark theme.</p>
                            <p class="mb-0">The header above should be <strong>dark gray (#212529)</strong> like the librarian dashboard.</p>
                        </div>
                        
                        <div class="text-center">
                            <a href="dashboard.php" class="btn btn-primary me-2">
                                <i class="bi bi-arrow-left me-1"></i>Back to Dashboard
                            </a>
                            <button onclick="location.reload()" class="btn btn-secondary">
                                <i class="bi bi-arrow-clockwise me-1"></i>Refresh Page
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Force header to be dark
        document.addEventListener('DOMContentLoaded', function() {
            const header = document.querySelector('header.navbar');
            if (header) {
                header.style.setProperty('background-color', '#212529', 'important');
                header.style.setProperty('background', '#212529', 'important');
                console.log('Test header forced to dark theme');
            }
        });
    </script>
</body>
</html>

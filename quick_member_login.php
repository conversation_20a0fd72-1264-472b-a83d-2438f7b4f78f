<?php
session_start();
require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

try {
    // Get test member
    $query = "SELECT * FROM members WHERE email = '<EMAIL>'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $member = $stmt->fetch();
    
    if ($member) {
        // Set session variables
        $_SESSION['member_id'] = $member['id'];
        $_SESSION['member_name'] = $member['first_name'] . ' ' . $member['last_name'];
        $_SESSION['member_email'] = $member['email'];
        
        // Redirect to member dashboard
        header('Location: member_dashboard.php');
        exit;
    } else {
        echo "Test member not found. Please run create_test_data.php first.";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>

<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if member is logged in
if (!isMemberLoggedIn()) {
    redirect('../member_login.php');
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get member ID from session
$member_id = $_SESSION['member_id'];

$success_message = '';
$error_message = '';

// Handle book renewal
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $loan_id = $_POST['loan_id'] ?? '';
    $renewal_reason = $_POST['renewal_reason'] ?? '';
    
    if (empty($loan_id)) {
        $error_message = "Invalid loan ID";
    } else {
        try {
            // Start transaction
            $db->beginTransaction();
            
            // Get loan details and verify ownership
            $query = "SELECT bl.*, b.title, b.author FROM book_loans bl 
                      JOIN books b ON bl.book_id = b.id 
                      WHERE bl.id = :loan_id AND bl.member_id = :member_id AND bl.status = 'borrowed'";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':loan_id', $loan_id);
            $stmt->bindParam(':member_id', $member_id);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $loan = $stmt->fetch();
                
                // Check if book can be renewed (not more than 2 renewals)
                $renewal_count = $loan['renewal_count'] ?? 0;
                if ($renewal_count >= 2) {
                    $error_message = "This book has already been renewed the maximum number of times (2).";
                } else {
                    // Check if there are any reservations for this book
                    $query = "SELECT COUNT(*) as reservation_count FROM book_reservations 
                              WHERE book_id = :book_id AND status = 'pending'";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':book_id', $loan['book_id']);
                    $stmt->execute();
                    $reservation_count = $stmt->fetch()['reservation_count'];
                    
                    if ($reservation_count > 0) {
                        $error_message = "This book cannot be renewed as there are pending reservations.";
                    } else {
                        // Calculate new due date (14 days from current due date)
                        $current_due_date = $loan['due_date'];
                        $new_due_date = date('Y-m-d', strtotime($current_due_date . ' + 14 days'));
                        
                        // Update loan with new due date and increment renewal count
                        $query = "UPDATE book_loans SET 
                                  due_date = :new_due_date, 
                                  renewal_count = renewal_count + 1,
                                  renewal_date = NOW()
                                  WHERE id = :loan_id";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':new_due_date', $new_due_date);
                        $stmt->bindParam(':loan_id', $loan_id);
                        $stmt->execute();
                        
                        // Log renewal activity
                        $activity_message = "Book renewed: {$loan['title']} - New due date: " . formatDate($new_due_date);
                        if (!empty($renewal_reason)) {
                            $activity_message .= " - Reason: " . $renewal_reason;
                        }
                        logActivity($db, 'renewal', $activity_message, 'book', $loan['book_id']);
                        
                        // Commit transaction
                        $db->commit();
                        
                        $success_message = "Book '{$loan['title']}' has been renewed successfully! New due date: " . formatDate($new_due_date);
                        
                        // Redirect back to dashboard after 3 seconds
                        header("refresh:3;url=../member_dashboard.php");
                    }
                }
            } else {
                $error_message = "Loan not found or you don't have permission to renew this book.";
            }
        } catch (PDOException $e) {
            $db->rollBack();
            $error_message = "Error renewing book: " . $e->getMessage();
        }
    }
}

// Get loan details if loan_id is provided
$loan = null;
if (isset($_GET['loan_id'])) {
    $loan_id = $_GET['loan_id'];
    $query = "SELECT bl.*, b.title, b.author, b.cover_image FROM book_loans bl 
              JOIN books b ON bl.book_id = b.id 
              WHERE bl.id = :loan_id AND bl.member_id = :member_id AND bl.status = 'borrowed'";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':loan_id', $loan_id);
    $stmt->bindParam(':member_id', $member_id);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $loan = $stmt->fetch();
    }
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php include '../includes/head.php'; ?>
    <title>Renew Book - Library Management System</title>
    <style>
        .renewal-card {
            max-width: 600px;
            margin: 0 auto;
        }
        .book-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .book-cover {
            width: 80px;
            height: 120px;
            object-fit: cover;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container mt-4">
        <div class="renewal-card">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-arrow-clockwise me-2"></i>Renew Book
                    </h4>
                </div>
                <div class="card-body">
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i><?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i><?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($loan): ?>
                        <div class="book-info">
                            <div class="row align-items-center">
                                <div class="col-auto">
                                    <?php if ($loan['cover_image']): ?>
                                        <img src="<?php echo h($loan['cover_image']); ?>" alt="Cover" class="book-cover">
                                    <?php else: ?>
                                        <div class="book-cover d-flex align-items-center justify-content-center bg-light text-dark">
                                            <i class="bi bi-book fs-1"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="col">
                                    <h5><?php echo h($loan['title']); ?></h5>
                                    <p class="mb-1">by <?php echo h($loan['author']); ?></p>
                                    <p class="mb-1"><strong>Current Due Date:</strong> <?php echo formatDate($loan['due_date']); ?></p>
                                    <p class="mb-0"><strong>Renewals Used:</strong> <?php echo $loan['renewal_count'] ?? 0; ?> of 2</p>
                                </div>
                            </div>
                        </div>

                        <?php if (($loan['renewal_count'] ?? 0) < 2): ?>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>Renewal Information:</strong>
                                <ul class="mb-0 mt-2">
                                    <li>This will extend your due date by 14 days</li>
                                    <li>New due date will be: <strong><?php echo formatDate(date('Y-m-d', strtotime($loan['due_date'] . ' + 14 days'))); ?></strong></li>
                                    <li>You can renew this book <?php echo 2 - ($loan['renewal_count'] ?? 0); ?> more time(s)</li>
                                </ul>
                            </div>

                            <form action="" method="post">
                                <input type="hidden" name="loan_id" value="<?php echo $loan['id']; ?>">
                                
                                <div class="mb-3">
                                    <label for="renewal_reason" class="form-label">Reason for Renewal (Optional)</label>
                                    <textarea class="form-control" id="renewal_reason" name="renewal_reason" rows="3" 
                                              placeholder="Why do you need to renew this book?"></textarea>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="bi bi-arrow-clockwise me-2"></i>Renew Book
                                    </button>
                                    <a href="../member_dashboard.php" class="btn btn-outline-secondary">
                                        <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
                                    </a>
                                </div>
                            </form>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <strong>Maximum Renewals Reached</strong><br>
                                This book has already been renewed the maximum number of times (2). 
                                Please return it by the due date or contact the library for assistance.
                            </div>
                            <div class="d-grid">
                                <a href="../member_dashboard.php" class="btn btn-primary">
                                    <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="bi bi-exclamation-circle display-1 text-warning"></i>
                            <h4 class="mt-3">Book Not Found</h4>
                            <p class="text-muted">The book you're trying to renew was not found or you don't have permission to renew it.</p>
                            <a href="../member_dashboard.php" class="btn btn-primary">
                                <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

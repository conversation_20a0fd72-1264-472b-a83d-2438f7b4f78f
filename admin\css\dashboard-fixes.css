/* Dashboard UI Fixes - Original Simple Design */

/* Simple body styling */
body {
    font-size: .875rem;
    background-color: #f8f9fa !important;
}

/* Simple Stats Cards - Original Design */
.stats-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 10px;
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
}

/* Ensure sign out button is always clickable */
.navbar-nav .btn-danger {
    z-index: 1060 !important;
    position: relative !important;
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* Simple navbar styling */
.navbar {
    background-color: #343a40 !important;
}

/* Simple sidebar styling */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #f8f9fa !important;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: .75rem 1rem;
}

.sidebar .nav-link.active {
    color: #2470dc;
}

.sidebar .nav-link:hover {
    color: #007bff;
}

/* Enhanced notification container positioning */
.static-notifications-container {
    z-index: 1055 !important;
    position: fixed !important;
    top: 60px !important;
    right: 10px !important;
    width: 380px !important;
    max-height: 500px !important;
    overflow-y: auto !important;
    background-color: #ffffff !important;
    color: #000000 !important;
    border: 2px solid #007bff !important;
    border-radius: 12px !important;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25) !important;
    display: none !important; /* Hidden by default */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    opacity: 0 !important;
    transform: translateY(-20px) scale(0.95) !important;
}

.static-notifications-container.show {
    opacity: 1 !important;
    transform: translateY(0) scale(1) !important;
    animation: slideInFromTop 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.static-notifications-container.minimized {
    max-height: 60px !important;
    overflow: hidden !important;
}

.static-notifications-container.minimized .notifications-body,
.static-notifications-container.minimized .notifications-footer {
    display: none !important;
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Simple notification container content styling */
.static-notifications-container .notifications-header {
    background: linear-gradient(135deg, #4361ee, #3a0ca3) !important;
    color: white !important;
    padding: 15px !important;
    border-radius: 10px 10px 0 0 !important;
}

.static-notifications-container .notifications-body {
    background-color: #ffffff !important;
    color: #000000 !important;
    padding: 15px !important;
}

.static-notifications-container .notifications-footer {
    background-color: #f8f9fa !important;
    color: #000000 !important;
    padding: 10px 15px !important;
    border-radius: 0 0 10px 10px !important;
    border-top: 1px solid #dee2e6 !important;
}

/* Ensure navbar elements don't overlap */
.navbar-nav {
    z-index: 1060 !important;
}

.navbar-nav .nav-item {
    z-index: 1060 !important;
    position: relative !important;
}

/* Fix alert positioning */
.alert {
    z-index: 1030 !important;
    position: relative !important;
}

/* Ensure buttons are clickable */
.btn {
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* Enhanced notification bell fixes */
.bell-icon, #notificationBell {
    cursor: pointer !important;
    z-index: 1060 !important;
    position: relative !important;
    pointer-events: auto !important;
    border: none !important;
    background: transparent !important;
    padding: 8px !important;
    border-radius: 50% !important;
    transition: all 0.3s ease !important;
    color: #ffffff !important;
}

#notificationBell:hover {
    opacity: 0.8 !important;
    transform: scale(1.1) !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
}

#notificationBell:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5) !important;
    outline-offset: 2px !important;
    box-shadow: none !important;
}

/* Make bell icon more visible */
.bell-icon i, #notificationBell i {
    color: #ffffff !important;
    font-size: 1.2rem !important;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5) !important;
}

/* Notification badge styling */
.notification-badge {
    background-color: #dc3545 !important;
    color: white !important;
    font-size: 0.75rem !important;
    font-weight: bold !important;
    animation: badgePulse 2s infinite !important;
}

@keyframes badgePulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 255, 255, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0); }
}

/* Enhanced notification bell styling - FIXED CLICKABILITY */
.notification-bell-btn {
    transition: all 0.3s ease !important;
    border-radius: 8px !important;
    position: relative !important;
    z-index: 1060 !important;
    pointer-events: auto !important;
    cursor: pointer !important;
    background: transparent !important;
    border: none !important;
}

.notification-bell-btn:hover {
    background-color: rgba(255, 255, 255, 0.15) !important;
    transform: scale(1.08) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

.notification-bell-btn:focus {
    outline: 2px solid rgba(255, 255, 255, 0.4) !important;
    outline-offset: 2px !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
}

.notification-bell-btn:active {
    transform: scale(0.98) !important;
}

/* Make bell icon more prominent and clickable */
.bell-icon,
.bell-icon i {
    font-size: 1.3rem !important;
    color: #ffffff !important;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3)) !important;
    pointer-events: none !important; /* Let clicks pass through to button */
}

/* Ensure notification badge doesn't block clicks */
.notification-badge {
    pointer-events: none !important;
    z-index: 1061 !important;
}

/* Enhanced notification badge */
.notification-badge {
    animation: pulse 2s infinite !important;
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7) !important;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
    }
    70% {
        transform: scale(1.1);
        box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
}

.pulse-animation {
    animation: pulse 2s infinite !important;
}

/* Spinning animation for refresh button */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.spin {
    animation: spin 1s linear infinite !important;
}

/* Header layout to match librarian dashboard exactly */
.navbar {
    background-color: #212529 !important;
    border-bottom: 1px solid #dee2e6 !important;
}

.navbar-brand {
    color: #ffffff !important;
    font-weight: 500 !important;
    text-decoration: none !important;
}

.navbar-brand:hover {
    color: #ffffff !important;
}

/* Welcome text styling to match librarian dashboard */
.navbar .nav-link.text-white {
    color: #ffffff !important;
    font-weight: normal !important;
    font-size: 0.875rem !important;
}

/* Sign out button styling */
.btn-danger {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    font-size: 0.875rem !important;
}

/* Responsive fixes */
@media (max-width: 767.98px) {
    .static-notifications-container {
        width: 90% !important;
        right: 5% !important;
        left: 5% !important;
    }
}

/* Ensure proper layering and dark theme for navbar */
.navbar {
    z-index: 1050 !important;
    background-color: #212529 !important;
}

.navbar-brand {
    color: #ffffff !important;
    font-weight: 500 !important;
}

.navbar-toggler {
    border-color: rgba(255, 255, 255, 0.1) !important;
}

/* Search form styling to match librarian dashboard exactly */
.form-control-dark {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #ffffff !important;
    font-size: 0.875rem !important;
    padding: 0.375rem 0.75rem !important;
}

.form-control-dark:focus {
    background-color: rgba(255, 255, 255, 0.15) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25) !important;
}

.form-control-dark::placeholder {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Search button styling */
.btn-outline-light {
    border-color: rgba(255, 255, 255, 0.2) !important;
    color: #ffffff !important;
    font-size: 0.875rem !important;
}

.btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
    color: #ffffff !important;
}

.sidebar {
    z-index: 1040 !important;
}

/* Fix for dropdown menus */
.dropdown-menu {
    z-index: 1055 !important;
}

/* Enhanced notification styling */
.notifications-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    color: white !important;
    padding: 12px 16px !important;
    border-radius: 12px 12px 0 0 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.notifications-header h6 {
    color: white !important;
    font-weight: 600 !important;
    margin: 0 !important;
}

.notifications-body {
    max-height: 350px !important;
    overflow-y: auto !important;
    padding: 8px !important;
}

.notification-item {
    padding: 12px !important;
    border-radius: 8px !important;
    margin-bottom: 8px !important;
    border-left: 4px solid #007bff !important;
    background: #f8f9fa !important;
    transition: all 0.2s ease !important;
}

.notification-item:hover {
    background: #e9ecef !important;
    transform: translateX(2px) !important;
}

.notification-item.unread {
    background: #fff3cd !important;
    border-left-color: #ffc107 !important;
}

.notifications-footer {
    padding: 12px 16px !important;
    background: #f8f9fa !important;
    border-radius: 0 0 12px 12px !important;
    border-top: 1px solid #dee2e6 !important;
}

/* Ensure modals appear above everything */
.modal {
    z-index: 1070 !important;
}

/* Fix for tooltips */
.tooltip {
    z-index: 1080 !important;
}

/* Ensure proper button styling */
.btn-danger {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: #fff !important;
}

.btn-danger:hover {
    background-color: #c82333 !important;
    border-color: #bd2130 !important;
    color: #fff !important;
}

/* Fix for notification close button */
.btn-close {
    z-index: 1061 !important;
    position: relative !important;
    pointer-events: auto !important;
}

/* Ensure proper spacing */
.navbar-nav .nav-item:last-child {
    margin-right: 0 !important;
}

/* Fix for notification header buttons */
.notifications-header .btn {
    z-index: 1041 !important;
    position: relative !important;
    pointer-events: auto !important;
}

/* Notification item styling */
.static-notifications-container .notification-item {
    padding: 12px 15px !important;
    border-bottom: 1px solid #dee2e6 !important;
    background-color: #ffffff !important;
    color: #000000 !important;
    transition: background-color 0.2s ease !important;
}

.static-notifications-container .notification-item:hover {
    background-color: #f8f9fa !important;
}

.static-notifications-container .notification-item:last-child {
    border-bottom: none !important;
}

.static-notifications-container .notification-item.unread {
    background-color: #e3f2fd !important;
    border-left: 4px solid #2196f3 !important;
}

.static-notifications-container .notification-icon {
    width: 32px !important;
    height: 32px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-right: 12px !important;
}

.static-notifications-container .notification-content {
    flex: 1 !important;
}

.static-notifications-container .notification-message {
    margin-bottom: 4px !important;
    font-size: 0.9rem !important;
    color: #000000 !important;
}

.static-notifications-container .notification-time {
    font-size: 0.8rem !important;
    color: #6c757d !important;
}

.static-notifications-container .mark-read-btn {
    font-size: 0.8rem !important;
    color: #007bff !important;
    text-decoration: none !important;
}

.static-notifications-container .mark-read-btn:hover {
    text-decoration: underline !important;
}

/* Fix for View Details links - ensure they are clickable */
.view-details-link {
    z-index: 1050 !important;
    position: relative !important;
    pointer-events: auto !important;
    cursor: pointer !important;
    text-decoration: none !important;
    display: inline-block !important;
    color: inherit !important;
}

.view-details-link:hover {
    opacity: 0.8 !important;
    text-decoration: underline !important;
    color: inherit !important;
}

.view-details-link:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5) !important;
    outline-offset: 2px !important;
}

/* Fix for card footer links */
.card-footer a {
    z-index: 1050 !important;
    position: relative !important;
    pointer-events: auto !important;
    cursor: pointer !important;
    display: inline-block !important;
    color: inherit !important;
}

.card-footer a:hover {
    color: inherit !important;
    opacity: 0.8 !important;
}

/* Ensure all links in dashboard are clickable */
.card a, .table a, .btn {
    pointer-events: auto !important;
    cursor: pointer !important;
    z-index: 1040 !important;
    position: relative !important;
}

/* Fix for stats cards hover effect not blocking links */
.stats-card {
    pointer-events: auto !important;
}

.stats-card .card-footer {
    pointer-events: auto !important;
    z-index: 1050 !important;
    position: relative !important;
}

/* Simple table styling */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Ensure table links are clickable */
.table a {
    color: #007bff !important;
    text-decoration: none !important;
    pointer-events: auto !important;
    cursor: pointer !important;
}

.table a:hover {
    color: #0056b3 !important;
    text-decoration: underline !important;
}

/* Responsive fixes */
@media (max-width: 767.98px) {
    .static-notifications-container {
        width: 90% !important;
        right: 5% !important;
        left: 5% !important;
    }

    .btn-toolbar {
        margin-top: 1rem;
        justify-content: center;
        width: 100%;
    }
}

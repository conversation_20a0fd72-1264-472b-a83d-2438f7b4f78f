<?php
require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

$success_messages = [];
$error_messages = [];

try {
    // Read and execute the SQL file
    $sql_file = 'database/create_fine_payments_table.sql';
    if (file_exists($sql_file)) {
        $sql_content = file_get_contents($sql_file);
        
        // Split SQL content into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql_content)));
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^--/', $statement)) {
                try {
                    $db->exec($statement);
                    if (stripos($statement, 'CREATE TABLE') !== false) {
                        preg_match('/CREATE TABLE.*?`?(\w+)`?/i', $statement, $matches);
                        if (isset($matches[1])) {
                            $success_messages[] = "✅ Table '{$matches[1]}' created/verified successfully";
                        }
                    } elseif (stripos($statement, 'ALTER TABLE') !== false) {
                        preg_match('/ALTER TABLE.*?`?(\w+)`?/i', $statement, $matches);
                        if (isset($matches[1])) {
                            $success_messages[] = "✅ Table '{$matches[1]}' altered successfully";
                        }
                    } elseif (stripos($statement, 'INSERT') !== false) {
                        $success_messages[] = "✅ Sample data inserted successfully";
                    }
                } catch (PDOException $e) {
                    // Ignore errors for IF NOT EXISTS and IF EXISTS statements
                    if (stripos($e->getMessage(), 'already exists') === false && 
                        stripos($e->getMessage(), 'Duplicate') === false) {
                        $error_messages[] = "❌ Error executing statement: " . $e->getMessage();
                    }
                }
            }
        }
    } else {
        $error_messages[] = "❌ SQL file not found: $sql_file";
    }
    
    // Verify tables exist
    $tables_to_check = ['fine_payments', 'member_wishlist', 'book_reviews'];
    foreach ($tables_to_check as $table) {
        $query = "SHOW TABLES LIKE '$table'";
        $stmt = $db->query($query);
        if ($stmt->rowCount() > 0) {
            $success_messages[] = "✅ Table '$table' exists and is ready";
        } else {
            $error_messages[] = "❌ Table '$table' was not created";
        }
    }
    
    // Check if book_loans table has new columns
    $query = "SHOW COLUMNS FROM book_loans LIKE 'renewal_count'";
    $stmt = $db->query($query);
    if ($stmt->rowCount() > 0) {
        $success_messages[] = "✅ Book loans table updated with renewal tracking";
    } else {
        $error_messages[] = "❌ Book loans table missing renewal columns";
    }
    
} catch (PDOException $e) {
    $error_messages[] = "❌ Database connection error: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Dashboard Enhancement Setup</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .setup-container {
            max-width: 800px;
            margin: 50px auto;
        }
        .feature-card {
            border-left: 4px solid #007bff;
            margin-bottom: 15px;
        }
        .success-item {
            color: #28a745;
            margin-bottom: 10px;
        }
        .error-item {
            color: #dc3545;
            margin-bottom: 10px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="setup-container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0">
                    <i class="bi bi-gear-fill me-2"></i>Member Dashboard Enhancement Setup
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>Setup Status:</strong> This script sets up the database tables and features needed for the enhanced member dashboard.
                </div>

                <?php if (!empty($success_messages)): ?>
                    <div class="alert alert-success">
                        <h5><i class="bi bi-check-circle me-2"></i>Successful Operations:</h5>
                        <?php foreach ($success_messages as $message): ?>
                            <div class="success-item"><?php echo $message; ?></div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_messages)): ?>
                    <div class="alert alert-danger">
                        <h5><i class="bi bi-exclamation-triangle me-2"></i>Errors:</h5>
                        <?php foreach ($error_messages as $message): ?>
                            <div class="error-item"><?php echo $message; ?></div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5><i class="bi bi-star-fill me-2"></i>New Features Added:</h5>
                        
                        <div class="card feature-card">
                            <div class="card-body">
                                <h6><i class="bi bi-plus-circle me-2"></i>Enhanced Navigation</h6>
                                <p class="mb-0">Quick access to borrow books, view loans, and manage wishlist with search functionality.</p>
                            </div>
                        </div>

                        <div class="card feature-card">
                            <div class="card-body">
                                <h6><i class="bi bi-exclamation-triangle me-2"></i>Smart Alerts</h6>
                                <p class="mb-0">Overdue book alerts, due soon notifications, and fine payment reminders.</p>
                            </div>
                        </div>

                        <div class="card feature-card">
                            <div class="card-body">
                                <h6><i class="bi bi-graph-up me-2"></i>Reading Statistics</h6>
                                <p class="mb-0">Track books read this year, favorite genres, and reading progress.</p>
                            </div>
                        </div>

                        <div class="card feature-card">
                            <div class="card-body">
                                <h6><i class="bi bi-lightning-fill me-2"></i>Quick Actions</h6>
                                <p class="mb-0">One-click access to borrow, return, browse catalog, and manage wishlist.</p>
                            </div>
                        </div>

                        <div class="card feature-card">
                            <div class="card-body">
                                <h6><i class="bi bi-credit-card me-2"></i>Fine Payment System</h6>
                                <p class="mb-0">Pay outstanding fines with multiple payment methods and tracking.</p>
                            </div>
                        </div>

                        <div class="card feature-card">
                            <div class="card-body">
                                <h6><i class="bi bi-arrow-clockwise me-2"></i>Book Renewal</h6>
                                <p class="mb-0">Renew books directly from dashboard with renewal tracking (up to 2 renewals).</p>
                            </div>
                        </div>

                        <div class="card feature-card">
                            <div class="card-body">
                                <h6><i class="bi bi-heart me-2"></i>Wishlist & Reviews</h6>
                                <p class="mb-0">Maintain personal book wishlist and rate/review books after reading.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4 text-center">
                    <a href="member_dashboard.php" class="btn btn-primary btn-lg me-3">
                        <i class="bi bi-speedometer2 me-2"></i>View Enhanced Dashboard
                    </a>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="bi bi-house me-2"></i>Back to Home
                    </a>
                </div>

                <div class="mt-4">
                    <div class="alert alert-warning">
                        <h6><i class="bi bi-info-circle me-2"></i>Next Steps:</h6>
                        <ol class="mb-0">
                            <li>Test the enhanced member dashboard by logging in as a member</li>
                            <li>Try borrowing books using the quick checkout feature</li>
                            <li>Test the return and rating functionality</li>
                            <li>Add books to your wishlist and explore recommendations</li>
                            <li>Test fine payment if you have any outstanding fines</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

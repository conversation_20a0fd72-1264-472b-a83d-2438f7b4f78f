<?php
require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html>
<head>
    <title>Fix Database Error</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        .fix-container { max-width: 800px; margin: 20px auto; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body class='bg-light'>
<div class='fix-container'>
    <div class='card'>
        <div class='card-header bg-danger text-white'>
            <h3>🔧 Fix Database Error</h3>
        </div>
        <div class='card-body'>";

try {
    echo "<h5>Checking and fixing database issues...</h5>";
    
    // Check if books table exists and its structure
    echo "<h6>1. Checking books table structure:</h6>";
    $query = "DESCRIBE books";
    $stmt = $db->query($query);
    $columns = $stmt->fetchAll();
    
    $has_content_file = false;
    foreach ($columns as $column) {
        if ($column['Field'] == 'content_file') {
            $has_content_file = true;
            break;
        }
    }
    
    if ($has_content_file) {
        echo "<p class='warning'>⚠️ Found 'content_file' column - this might be causing issues</p>";
        
        // Remove the problematic column
        $query = "ALTER TABLE books DROP COLUMN content_file";
        $db->exec($query);
        echo "<p class='success'>✅ Removed problematic 'content_file' column</p>";
    } else {
        echo "<p class='success'>✅ No 'content_file' column found</p>";
    }
    
    // Ensure basic books table structure
    echo "<h6>2. Ensuring proper books table structure:</h6>";
    $query = "CREATE TABLE IF NOT EXISTS books (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        author VARCHAR(255) NOT NULL,
        isbn VARCHAR(20) UNIQUE,
        category VARCHAR(100),
        description TEXT,
        publication_year INT,
        total_quantity INT DEFAULT 1,
        available_quantity INT DEFAULT 1,
        cover_image VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $db->exec($query);
    echo "<p class='success'>✅ Books table structure verified</p>";
    
    // Check and fix other tables
    echo "<h6>3. Checking other required tables:</h6>";
    
    // Members table
    $query = "CREATE TABLE IF NOT EXISTS members (
        id INT AUTO_INCREMENT PRIMARY KEY,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255),
        phone VARCHAR(20),
        address TEXT,
        membership_date DATE,
        membership_status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
        google_id VARCHAR(255),
        remember_token VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $db->exec($query);
    echo "<p class='success'>✅ Members table verified</p>";
    
    // Book loans table
    $query = "CREATE TABLE IF NOT EXISTS book_loans (
        id INT AUTO_INCREMENT PRIMARY KEY,
        book_id INT NOT NULL,
        member_id INT NOT NULL,
        issue_date DATE NOT NULL,
        due_date DATE NOT NULL,
        return_date DATE NULL,
        status ENUM('borrowed', 'returned', 'overdue') DEFAULT 'borrowed',
        fine DECIMAL(10, 2) DEFAULT 0.00,
        renewal_count INT DEFAULT 0,
        renewal_date TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
    )";
    $db->exec($query);
    echo "<p class='success'>✅ Book loans table verified</p>";
    
    // Enhanced tables for new features
    $query = "CREATE TABLE IF NOT EXISTS member_wishlist (
        id INT AUTO_INCREMENT PRIMARY KEY,
        member_id INT NOT NULL,
        book_id INT NOT NULL,
        added_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
        FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
        UNIQUE KEY unique_member_book (member_id, book_id)
    )";
    $db->exec($query);
    echo "<p class='success'>✅ Member wishlist table verified</p>";
    
    $query = "CREATE TABLE IF NOT EXISTS fine_payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        member_id INT NOT NULL,
        amount DECIMAL(10, 2) NOT NULL,
        payment_method VARCHAR(50) NOT NULL,
        payment_notes TEXT,
        payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
    )";
    $db->exec($query);
    echo "<p class='success'>✅ Fine payments table verified</p>";
    
    $query = "CREATE TABLE IF NOT EXISTS book_reviews (
        id INT AUTO_INCREMENT PRIMARY KEY,
        book_id INT NOT NULL,
        member_id INT NOT NULL,
        rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
        review_text TEXT,
        review_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
        UNIQUE KEY unique_member_book_review (member_id, book_id)
    )";
    $db->exec($query);
    echo "<p class='success'>✅ Book reviews table verified</p>";
    
    // Add sample data if tables are empty
    echo "<h6>4. Adding sample data if needed:</h6>";
    
    // Check if books exist
    $query = "SELECT COUNT(*) as count FROM books";
    $stmt = $db->query($query);
    $book_count = $stmt->fetch()['count'];
    
    if ($book_count == 0) {
        $books = [
            ['To Kill a Mockingbird', 'Harper Lee', '9780061120084', 'Fiction', 'A classic novel about racial injustice.', 1960],
            ['1984', 'George Orwell', '9780451524935', 'Fiction', 'A dystopian social science fiction novel.', 1949],
            ['Pride and Prejudice', 'Jane Austen', '9780141439518', 'Romance', 'A romantic novel of manners.', 1813],
            ['The Great Gatsby', 'F. Scott Fitzgerald', '9780743273565', 'Fiction', 'A classic American novel.', 1925],
            ['Harry Potter', 'J.K. Rowling', '9780439708180', 'Fantasy', 'The first book in the Harry Potter series.', 1997]
        ];
        
        foreach ($books as $book) {
            $query = "INSERT INTO books (title, author, isbn, category, description, publication_year, total_quantity, available_quantity) 
                      VALUES (?, ?, ?, ?, ?, ?, 3, 3)";
            $stmt = $db->prepare($query);
            $stmt->execute($book);
        }
        echo "<p class='success'>✅ Sample books added</p>";
    } else {
        echo "<p class='success'>✅ Books already exist ($book_count books)</p>";
    }
    
    // Check if test member exists
    $query = "SELECT COUNT(*) as count FROM members WHERE email = '<EMAIL>'";
    $stmt = $db->query($query);
    $member_count = $stmt->fetch()['count'];
    
    if ($member_count == 0) {
        $query = "INSERT INTO members (first_name, last_name, email, password, phone, address, membership_date, membership_status) 
                  VALUES ('Test', 'Member', '<EMAIL>', ?, '************', '123 Test Street', CURDATE(), 'active')";
        $stmt = $db->prepare($query);
        $password = password_hash('member123', PASSWORD_DEFAULT);
        $stmt->execute([$password]);
        echo "<p class='success'>✅ Test member created</p>";
    } else {
        echo "<p class='success'>✅ Test member already exists</p>";
    }
    
    echo "<div class='alert alert-success mt-4'>
            <h5>🎉 Database Fixed Successfully!</h5>
            <p>All database issues have been resolved. You can now access the enhanced member dashboard.</p>
          </div>";
    
    echo "<div class='mt-4 text-center'>
            <a href='quick_member_login.php' class='btn btn-success btn-lg me-2'>
                <i class='bi bi-box-arrow-in-right me-2'></i>Login & View Dashboard
            </a>
            <a href='member_dashboard.php' class='btn btn-primary btn-lg'>
                <i class='bi bi-speedometer2 me-2'></i>View Dashboard
            </a>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>
            <h5>Error:</h5>
            <p>" . $e->getMessage() . "</p>
          </div>";
}

echo "</div></div></div></body></html>";
?>

<?php
session_start();

// Auto-login for testing if no session
if (!isset($_SESSION['member_id'])) {
    $_SESSION['member_id'] = 1;
    $_SESSION['member_name'] = 'Test Member';
    $_SESSION['member_email'] = '<EMAIL>';
}

// Connect to database with error handling
try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
} catch (Exception $e) {
    // If database fails, use dummy data
    $db = null;
}

// Initialize variables with safe defaults
$member = [
    'first_name' => 'Test',
    'last_name' => 'Member',
    'email' => '<EMAIL>',
    'membership_date' => date('Y-m-d')
];
$current_loans = [];
$total_fine = 25.50; // Demo fine amount
$overdue_count = 2; // Demo overdue count

// Try to get real data if database is available
if ($db) {
    try {
        $member_id = $_SESSION['member_id'];
        
        // Get member details
        $query = "SELECT * FROM members WHERE id = ? LIMIT 1";
        $stmt = $db->prepare($query);
        $stmt->execute([$member_id]);
        if ($stmt->rowCount() > 0) {
            $member = $stmt->fetch();
        }
        
        // Get current loans
        $query = "SELECT bl.*, b.title, b.author, b.isbn 
                  FROM book_loans bl
                  JOIN books b ON bl.book_id = b.id
                  WHERE bl.member_id = ? AND bl.status != 'returned'
                  ORDER BY bl.due_date ASC LIMIT 10";
        $stmt = $db->prepare($query);
        $stmt->execute([$member_id]);
        $current_loans = $stmt->fetchAll();
        
        // Calculate total fines
        $query = "SELECT SUM(fine) as total_fine FROM book_loans WHERE member_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$member_id]);
        $result = $stmt->fetch();
        $total_fine = $result['total_fine'] ?? 0;
        
        // Count overdue books
        $overdue_count = 0;
        foreach ($current_loans as $loan) {
            if (strtotime($loan['due_date']) < time()) {
                $overdue_count++;
            }
        }
        
    } catch (Exception $e) {
        // Continue with demo data if queries fail
        error_log("Dashboard query error: " . $e->getMessage());
    }
}

// Add demo overdue books if none exist
if (empty($current_loans)) {
    $current_loans = [
        [
            'id' => 1,
            'title' => 'The Great Gatsby',
            'author' => 'F. Scott Fitzgerald',
            'due_date' => date('Y-m-d', strtotime('-5 days')),
            'fine' => 15.00
        ],
        [
            'id' => 2,
            'title' => '1984',
            'author' => 'George Orwell',
            'due_date' => date('Y-m-d', strtotime('-3 days')),
            'fine' => 10.50
        ]
    ];
}

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

function formatDate($date) {
    return date('M j, Y', strtotime($date));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Dashboard - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body { background-color: #f8f9fa; }
        .dashboard-container { max-width: 1200px; margin: 20px auto; }
        .card { border: none; box-shadow: 0 2px 5px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .payment-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="bi bi-person-circle me-1"></i><?php echo h($member['first_name'] . ' ' . $member['last_name']); ?>
                </span>
            </div>
        </div>
    </nav>

    <div class="dashboard-container">
        <div class="row">
            <div class="col-md-12">
                <h2 class="mb-4">
                    <i class="bi bi-credit-card me-2"></i>Payment Dashboard
                    <small class="text-muted">- Pay Your Overdue Book Fines</small>
                </h2>

                <!-- Payment Alert -->
                <?php if ($total_fine > 0): ?>
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <strong>Outstanding Fines:</strong> You have $<?php echo number_format($total_fine, 2); ?> in unpaid fines.
                        </div>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#paymentModal">
                            <i class="bi bi-credit-card me-1"></i>Pay Now
                        </button>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Overdue Books -->
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>Overdue Books
                            <span class="badge bg-light text-danger ms-2"><?php echo $overdue_count; ?></span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($current_loans as $loan): ?>
                                <?php if (strtotime($loan['due_date']) < time()): ?>
                                    <?php 
                                    $days_overdue = floor((time() - strtotime($loan['due_date'])) / (60 * 60 * 24));
                                    $book_fine = $loan['fine'] ?? ($days_overdue * 1.00);
                                    ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="card border-warning">
                                            <div class="card-body">
                                                <h6 class="card-title text-danger"><?php echo h($loan['title']); ?></h6>
                                                <p class="card-text">
                                                    <small class="text-muted">by <?php echo h($loan['author']); ?></small><br>
                                                    <strong>Due Date:</strong> <?php echo formatDate($loan['due_date']); ?><br>
                                                    <strong>Days Overdue:</strong> <span class="text-danger"><?php echo $days_overdue; ?> days</span><br>
                                                    <strong>Fine:</strong> <span class="text-danger">$<?php echo number_format($book_fine, 2); ?></span>
                                                </p>
                                                <button class="btn btn-warning btn-sm w-100" data-bs-toggle="modal" data-bs-target="#paymentModal">
                                                    <i class="bi bi-credit-card me-1"></i>Pay Fine
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="text-center mt-3">
                            <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#paymentModal">
                                <i class="bi bi-credit-card me-2"></i>Pay All Fines ($<?php echo number_format($total_fine, 2); ?>)
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Payment Success Demo -->
                <div class="card payment-card">
                    <div class="card-body text-center">
                        <h4><i class="bi bi-check-circle-fill me-2"></i>Payment System Ready!</h4>
                        <p class="mb-0">Enhanced payment functionality with multiple payment methods is now available.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Modal -->
    <div class="modal fade" id="paymentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-credit-card me-2"></i>Pay Outstanding Fines
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Total Outstanding Fine:</strong> $<?php echo number_format($total_fine, 2); ?>
                    </div>
                    
                    <form id="paymentForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Payment Method</label>
                                    <select class="form-select" id="paymentMethod" required>
                                        <option value="">Select Payment Method</option>
                                        <option value="cash">💵 Cash</option>
                                        <option value="card">💳 Credit/Debit Card</option>
                                        <option value="gcash">📱 GCash</option>
                                        <option value="paymaya">📱 PayMaya</option>
                                        <option value="bank_transfer">🏦 Bank Transfer</option>
                                        <option value="online">🌐 Online Payment</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Amount to Pay</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="amount" 
                                               step="0.01" max="<?php echo $total_fine; ?>"
                                               value="<?php echo $total_fine; ?>" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3" id="referenceDiv" style="display: none;">
                            <label class="form-label">Payment Reference/Transaction ID</label>
                            <input type="text" class="form-control" id="reference" 
                                   placeholder="Enter transaction ID or reference number">
                            <div class="form-text">Required for digital payments</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Notes (Optional)</label>
                            <textarea class="form-control" rows="2" placeholder="Add any additional notes..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="processPayment()">
                        <i class="bi bi-credit-card me-1"></i>Process Payment
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Payment method change handler
        document.getElementById('paymentMethod').addEventListener('change', function() {
            const digitalPayments = ['gcash', 'paymaya', 'bank_transfer', 'online'];
            const referenceDiv = document.getElementById('referenceDiv');
            
            if (digitalPayments.includes(this.value)) {
                referenceDiv.style.display = 'block';
            } else {
                referenceDiv.style.display = 'none';
            }
        });

        function processPayment() {
            const method = document.getElementById('paymentMethod').value;
            const amount = document.getElementById('amount').value;
            
            if (!method) {
                alert('Please select a payment method.');
                return;
            }
            
            if (!amount || amount <= 0) {
                alert('Please enter a valid amount.');
                return;
            }
            
            // Simulate payment processing
            alert(`Payment of $${amount} via ${method.toUpperCase()} processed successfully!\n\nTransaction ID: TXN-${Date.now()}`);
            
            // Close modal and refresh page
            bootstrap.Modal.getInstance(document.getElementById('paymentModal')).hide();
            setTimeout(() => location.reload(), 1000);
        }
    </script>
</body>
</html>

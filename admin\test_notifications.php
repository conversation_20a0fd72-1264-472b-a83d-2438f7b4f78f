<?php
/**
 * Test Notifications - Add sample notifications for testing
 */

session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../login.php');
}

// Connect to database
try {
    $database = new Database();
    $db = $database->getConnection();
} catch (Exception $e) {
    die("Database Error: " . $e->getMessage());
}

// Add test notifications if requested
if (isset($_POST['add_test_notifications'])) {
    $user_id = $_SESSION['user_id'];
    
    // Sample notifications
    $test_notifications = [
        [
            'message' => 'Welcome to the enhanced admin dashboard! Your notification system is now fully functional.',
            'type' => 'success',
            'user_id' => $user_id
        ],
        [
            'message' => 'System backup completed successfully. All data is secure.',
            'type' => 'info',
            'user_id' => $user_id
        ],
        [
            'message' => 'Warning: 3 books are overdue and require immediate attention.',
            'type' => 'warning',
            'user_id' => $user_id
        ],
        [
            'message' => 'New member registration: John Doe has joined the library.',
            'type' => 'info',
            'user_id' => $user_id
        ],
        [
            'message' => 'Critical: Database maintenance scheduled for tonight at 2 AM.',
            'type' => 'danger',
            'user_id' => $user_id
        ]
    ];
    
    $added_count = 0;
    foreach ($test_notifications as $notification) {
        if (addNotification($db, $notification['message'], $notification['type'], $notification['user_id'])) {
            $added_count++;
        }
    }
    
    setMessage("Added $added_count test notifications successfully!", 'success');
    redirect('dashboard.php');
}

// Clear all notifications if requested
if (isset($_POST['clear_notifications'])) {
    $user_id = $_SESSION['user_id'];
    
    $query = "DELETE FROM notifications WHERE user_id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    
    setMessage('All notifications cleared successfully!', 'success');
    redirect('dashboard.php');
}

// Get current notification count
$user_id = $_SESSION['user_id'];
$query = "SELECT COUNT(*) as count FROM notifications WHERE user_id = :user_id";
$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$notification_count = $stmt->fetch()['count'] ?? 0;

$query = "SELECT COUNT(*) as count FROM notifications WHERE user_id = :user_id AND is_read = 0";
$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$unread_count = $stmt->fetch()['count'] ?? 0;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Notifications - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="bi bi-bell me-2"></i>Test Notifications</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle me-2"></i>Current Status</h6>
                            <p class="mb-1"><strong>Total Notifications:</strong> <?php echo $notification_count; ?></p>
                            <p class="mb-0"><strong>Unread Notifications:</strong> <?php echo $unread_count; ?></p>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <i class="bi bi-plus-circle fs-1 text-success mb-3"></i>
                                        <h5>Add Test Notifications</h5>
                                        <p class="text-muted">Add 5 sample notifications to test the system</p>
                                        <form method="post">
                                            <button type="submit" name="add_test_notifications" class="btn btn-success">
                                                <i class="bi bi-plus-lg me-1"></i>Add Test Notifications
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <div class="card border-danger">
                                    <div class="card-body text-center">
                                        <i class="bi bi-trash fs-1 text-danger mb-3"></i>
                                        <h5>Clear Notifications</h5>
                                        <p class="text-muted">Remove all notifications for testing</p>
                                        <form method="post" onsubmit="return confirm('Are you sure you want to clear all notifications?')">
                                            <button type="submit" name="clear_notifications" class="btn btn-danger">
                                                <i class="bi bi-trash me-1"></i>Clear All Notifications
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="dashboard.php" class="btn btn-primary">
                                <i class="bi bi-arrow-left me-1"></i>Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
// Include config file if not already included
if (!defined('BASE_URL')) {
    require_once __DIR__ . '/../config/config.php';
}
?>
<header class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
    <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="<?php echo (function_exists('isAdmin') && isAdmin()) ? url('admin/dashboard.php') : url('index.php'); ?>">
        <i class="bi bi-book me-2"></i>Library MS
    </a>
    <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="w-100">
        <form class="d-none d-md-flex w-50 mx-auto" action="<?php echo url('search.php'); ?>" method="get">
            <input class="form-control form-control-dark" type="text" name="q" placeholder="Search books, members..." aria-label="Search" required>
            <button class="btn btn-dark" type="submit"><i class="bi bi-search"></i></button>
        </form>
    </div>
    <div class="navbar-nav">
        <div class="nav-item text-nowrap d-flex align-items-center">
            <!-- Notifications Dropdown -->
            <?php if (isset($notifications) && isset($unread_count)): ?>
            <div class="nav-item dropdown me-2">
                <a class="nav-link position-relative dropdown-toggle"
                   href="#"
                   id="notificationDropdown"
                   role="button"
                   data-bs-toggle="dropdown"
                   aria-expanded="false"
                   data-bs-auto-close="outside">
                    <span class="bell-icon">
                        <i class="bi bi-bell fs-5 text-white"></i>
                    </span>
                    <?php if ($unread_count > 0): ?>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-badge" id="notificationBadge">
                            <?php echo $unread_count > 9 ? '9+' : $unread_count; ?>
                        </span>
                    <?php endif; ?>
                </a>

                <!-- Dropdown Menu -->
                <div class="dropdown-menu dropdown-menu-end notifications-dropdown" aria-labelledby="notificationDropdown">
                    <!-- Header -->
                    <div class="notifications-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0 text-white">
                                <i class="bi bi-bell me-2"></i>Recent Notifications
                            </h6>
                            <span class="badge bg-light text-dark" id="notificationCount">
                                <?php echo $unread_count; ?> new
                            </span>
                        </div>
                    </div>

                    <!-- Notifications List -->
                    <div class="notifications-body" id="notificationsList">
                        <?php if (!empty($notifications)): ?>
                            <?php foreach (array_slice($notifications, 0, 5) as $notification): ?>
                                <div class="notification-item <?php echo $notification['is_read'] ? '' : 'unread'; ?>"
                                     data-notification-id="<?php echo $notification['id']; ?>">
                                    <div class="d-flex">
                                        <div class="notification-icon me-3">
                                            <?php
                                            $icon_class = 'bi-info-circle text-info';
                                            switch($notification['type']) {
                                                case 'overdue':
                                                    $icon_class = 'bi-exclamation-triangle text-warning';
                                                    break;
                                                case 'due_today':
                                                    $icon_class = 'bi-calendar-check text-primary';
                                                    break;
                                                case 'new_member':
                                                    $icon_class = 'bi-person-plus text-success';
                                                    break;
                                                case 'system':
                                                    $icon_class = 'bi-gear text-secondary';
                                                    break;
                                            }
                                            ?>
                                            <i class="bi <?php echo $icon_class; ?>"></i>
                                        </div>
                                        <div class="notification-content flex-grow-1">
                                            <div class="notification-title">
                                                <?php echo htmlspecialchars($notification['title']); ?>
                                            </div>
                                            <div class="notification-message">
                                                <?php echo htmlspecialchars($notification['message']); ?>
                                            </div>
                                            <div class="notification-time">
                                                <i class="bi bi-clock me-1"></i>
                                                <?php echo timeAgo($notification['created_at']); ?>
                                            </div>
                                        </div>
                                        <?php if (!$notification['is_read']): ?>
                                            <div class="notification-actions">
                                                <button class="btn btn-sm btn-outline-primary mark-read-btn"
                                                        data-notification-id="<?php echo $notification['id']; ?>"
                                                        title="Mark as read">
                                                    <i class="bi bi-check"></i>
                                                </button>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="notification-item text-center py-4">
                                <i class="bi bi-bell-slash fs-3 text-muted mb-2"></i>
                                <div class="text-muted">No notifications yet</div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Footer -->
                    <div class="notifications-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <?php if ($unread_count > 0): ?>
                                <button class="btn btn-sm btn-outline-primary" id="markAllReadBtn">
                                    <i class="bi bi-check-all me-1"></i>Mark all as read
                                </button>
                            <?php else: ?>
                                <span class="text-muted small">All caught up!</span>
                            <?php endif; ?>
                            <a href="<?php echo url('notifications/index.php'); ?>" class="btn btn-sm btn-primary">
                                <i class="bi bi-list me-1"></i>View all
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <button id="darkModeToggle" class="btn btn-outline-light me-2" title="Toggle Dark Mode">
                <i id="darkModeIcon" class="bi bi-moon"></i>
            </button>
            <span class="nav-link px-3 text-white">Welcome, <?php echo isset($_SESSION['username']) ? $_SESSION['username'] : 'Guest'; ?></span>
            <a class="nav-link px-3" href="<?php echo url('logout.php'); ?>">Sign out</a>
        </div>
    </div>
</header>
